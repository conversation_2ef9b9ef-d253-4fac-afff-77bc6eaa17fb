package com.circinus.http.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.circinus.comm.CTILog;

public class SPUtils {
    public static final String TAG = "SPUtils";
    public static String SP_NAME = "CentauriHTTPSP";
    public static String ACP_MODEL = "ACP_MODEL" + System.currentTimeMillis();
    public static String ALGORITHM_MODEL = "AL_MODEL" + System.currentTimeMillis();
    public static String FK_MODEL = "FL_MODEL" + System.currentTimeMillis();
    public static String SK_MODEL = "SL_MODEL" + System.currentTimeMillis();
    public static String TK_MODEL = "TL_MODEL" + System.currentTimeMillis();
    public static Context mContext;

    public static void init(Context context) {
        if (context == null) {
            CTILog.e(TAG, "http-core, init context null!");
            return;
        }
        mContext = context.getApplicationContext();
        clear();
    }

    public static String getString(String key) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, getString context null!");
            return "";
        }
        return mContext.getSharedPreferences(SP_NAME, 0).getString(key, "");
    }

    public static String getString(String key, String def) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, getString context null!");
            return "";
        }
        return mContext.getSharedPreferences(SP_NAME, 0).getString(key, def);
    }

    public static void putString(String key, String value) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, putString context null!");
            return;
        }
        mContext.getSharedPreferences(SP_NAME, 0).edit()
                .putString(key, value)
                .apply();
    }

    public static void putString(String spName, String key, String value) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, putString context null!");
            return;
        }
        mContext.getSharedPreferences(spName, 0).edit()
                .putString(key, value)
                .apply();
    }

    public static void putBoolean(String key, boolean value) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, putBoolean context null!");
            return;
        }
        mContext.getSharedPreferences(SP_NAME, 0).edit()
                .putBoolean(key, value)
                .apply();
    }

    public static int getInt(String key) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, getInt context null!");
            return 0;
        }
        return mContext.getSharedPreferences(SP_NAME, 0).getInt(key, 0);
    }

    public static boolean getBoolean(String key) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, getBoolean context null!");
            return false;
        }
        return mContext.getSharedPreferences(SP_NAME, 0).getBoolean(key, true);
    }

    public static void putInt(String key, int value) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, putInt context null!");
            return;
        }
        mContext.getSharedPreferences(SP_NAME, 0).edit()
                .putInt(key, value)
                .apply();
    }

    public static void clear() {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, clear context null!");
            return;
        }
        mContext.getSharedPreferences(SP_NAME, 0).edit()
                .clear()
                .apply();
    }

    public static SharedPreferences getSP(String spName) {
        if (mContext == null) {
            CTILog.e(TAG, "http-core, SharedPreferences context null!");
            return null;
        }
        return mContext.getSharedPreferences(spName, Context.MODE_PRIVATE);
    }
}
