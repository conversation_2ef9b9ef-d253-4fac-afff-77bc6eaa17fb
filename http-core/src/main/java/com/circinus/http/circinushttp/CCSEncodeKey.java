package com.circinus.http.circinushttp;

import android.util.Log;

import com.circinus.http.core.HttpLog;
import com.circinus.http.utils.SPUtils;

import java.io.UnsupportedEncodingException;
import java.security.SecureRandom;

/**
 * Indicates the key used for a certain encryption, and there are mainly two members: key type and key content
 * Created by a1w0n on 2017/7/7.
 */
public class CTIEncodeKey {

    public static byte[] randomBytes = new byte[40];
    public static String MIX_MD5;
    public static String strEncodeKey;

    static {
        SecureRandom random = new SecureRandom();
        random.nextBytes(randomBytes);
        try {
            strEncodeKey = new String(CTIEncodeKey.randomBytes, "ASCII");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            strEncodeKey = new String(CTIEncodeKey.randomBytes);
        }
        MIX_MD5 = "1234567890123456" + random.nextInt();
    }

    // The type of key used for encryption
    @SuppressWarnings("WeakerAccess")
    public String keyType = ICTIEncodeKeyType.ENCODE_KEY_TYPE_BASE;

    // The content of the key used for encryption
    @SuppressWarnings("WeakerAccess")
    public String key = "";


    public String getEKey() {
        if (null == SPUtils.getString(SPUtils.FK_MODEL, null)
                || null == SPUtils.getString(SPUtils.SK_MODEL, null)
                || null == SPUtils.getString(SPUtils.TK_MODEL, null)) {
            return key;
        }
        return SPUtils.getString(SPUtils.FK_MODEL)
                + SPUtils.getString(SPUtils.SK_MODEL)
                + SPUtils.getString(SPUtils.TK_MODEL);
    }

    public void setEKey(String key) {
        if (null != key) {
            SPUtils.putString(SPUtils.FK_MODEL, key.substring(0, key.length() / 3));
            SPUtils.putString(SPUtils.SK_MODEL, key.substring(key.length() / 3, key.length() / 3 * 2));
            SPUtils.putString(SPUtils.TK_MODEL, key.substring(key.length() / 3 * 2));
        }
        this.key = key;
    }
}
