package com.circinus.http.circinushttp;

import android.text.TextUtils;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Responsible for updating the IP database after each Http request is successful
 *
 * Created by a1w0n on 16/11/28.
 */
final class CTIIPMeasureHandler extends CTIBaseHttpHandler {

    private final CTINetworkManager newNetworkManager;

    CTIIPMeasureHandler(final CTINetworkManager networkManager) {
        this.newNetworkManager = networkManager;
    }

    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        if (request == null) {
            return;
        }

        if (response == null) {
            return;
        }

        if (newNetworkManager == null) {
            return;
        }

        // If it is not the network request of Master <PERSON>
        if (!(request instanceof CTIHttpRequest)) {
            return;
        }

        final String host = request.getHost();
        if (TextUtils.isEmpty(host)) {
            return;
        }

        // If this network request is successful, that is, Http return code == 200
        if (response.isSuccess()) {
            newNetworkManager.updateIPTimes(host);
        }
    }
}
