package com.circinus.http.circinushttp;

import android.content.Context;
import android.text.TextUtils;
import com.circinus.comm.CTILog;

import com.circinus.http.core.Response;

import org.apache.http.conn.ConnectTimeoutException;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * The result of Mi Master network request corresponding to CentauriHttpRequest
 * Created by a1w0n on 16/11/16.
 */
public class CTIHttpResponse {

    // Unparseable Json data
    private static final int RESULT_CODE_ERROR_JSON = -101;
    // There is no ret field in the Json data, or the ret field is empty
    private static final int RESULT_CODE_JSON_RET_EMPTY = -102;
    // Json data ret field cannot be parsed into numbers
    private static final int RESULT_CODE_JSON_RET_NOT_NUM = -103;
    // An error occurred when constructing Json data to the outside
    private static final int RESULT_CODE_JSON_CONSTRUCT_ERROR = -104;

    // network system error
    private static final int ERROR_NETWORK_SYSTEM = 20000;
    // network connection timed out
    private static final int ERROR_NETWORK_CONTIMEOUT = 20001;
    // network response timeout
    private static final int ERROR_NETWORK_READTIMEOUT = 20002;
    // Network system IO error
    private static final int ERROR_NETWORK_IOEXCEPTION = 20005;

    // The certificate time is not verified
    private static final int ERROR_NETWORK_HTTPSTIMES = 20100;
    // This cmwap does not support https certificates
    private static final int ERROR_NETWORK_HTTPSCMWAP = 20101;
    // wifi has set a proxy
    private static final int ERROR_NETWORK_HTTPSWIFIPROXY = 20102;

    // Any indescribable error, use this to call back to the outside
    private static final String RESULT_MSG_SYSTEM_BUSY = "The system is busy, please try again later";

    // The return code on Master Mi’s business is different from the Http return code
    // The value of this field comes from the value of the ret field in the json data returned by Master Mi CGI
    // This value == 0 represents the success of Master Mi's business
    // Http return code == 200 means the network request is successful
    int centauriBusinessResultCode = -1;

    // The return code of the Http request, if it is 200, it means that the Http request was successful
    // Note the difference between this and centauriBusinessResultCode
    @SuppressWarnings("WeakerAccess")
    int httpResultCode = 100;

    // Error message after encapsulation by Mi Master SDK
    String centauriBusinessResultMsg = "default_resultMsg";
    // String string returned after Http request
    public String resultData = "";

    public Exception exception;

    CTIHttpResponse(final Context context, final Response response) {
        if (response == null) {
            return;
        }

        resultData = response.responseBody;
        exception = response.exception;
        httpResultCode = response.resultCode;

        if (response.isSuccess()) { // If the Http request is successful
            handleSuccess(response);

        } else if (response.hasException()) { // If Http fails and an exception occurs
            if (context == null) {
                CTILog.e("CTIHttpResponse", "http-core, context null!");
                return;
            }
            handleException(context, response);

        } else if (!response.isResultCodeOK()) { // If Http fails, no exception has occurred, the return code is not 200
            centauriBusinessResultMsg = "Network error (error code" + response.resultCode + ")";

        } else if (!response.hasNotEmptyBody()) {
            // If Http fails and no exception occurs, the return code is 200, but the body is empty
            centauriBusinessResultMsg = "Network connection returns empty results, please try again later";
        }
    }

    /**
     * Handling scenarios where Http connection is abnormal
     */
    private void handleException(final Context context, final Response response) {
        final Exception e = response.exception;

        if (e instanceof ConnectTimeoutException) {
            centauriBusinessResultMsg = "Network connection timed out, please check the network";
            centauriBusinessResultCode = ERROR_NETWORK_CONTIMEOUT;

        } else if (e instanceof SocketTimeoutException) {
            centauriBusinessResultMsg = "The network response timed out, please check the network";
            centauriBusinessResultCode = ERROR_NETWORK_READTIMEOUT;

        } else if (e instanceof IOException) {
            centauriBusinessResultMsg = "The network connection is abnormal, please check the network";
            centauriBusinessResultCode = ERROR_NETWORK_IOEXCEPTION;

        } else {
            centauriBusinessResultMsg = "Network error, please try again later";
            centauriBusinessResultCode = ERROR_NETWORK_SYSTEM;
        }

        if (response.isHttpsResponse()) {
            Throwable t = response.exception;
            while (t != null) {
                if (CTIHttpModuleTools.isTimeError(t)) {
                    centauriBusinessResultCode = ERROR_NETWORK_HTTPSTIMES;
                    centauriBusinessResultMsg =
                            "Your device system time is incorrect, please change it"
                                    + getErrorCode(ERROR_NETWORK_HTTPSTIMES);
                }

                if (CTIHttpModuleTools.isWifiProxy(t, context)) {
                    centauriBusinessResultCode = ERROR_NETWORK_HTTPSWIFIPROXY;
                    centauriBusinessResultMsg =
                            "Your wifi has a proxy set up, please change it"
                                    + getErrorCode(ERROR_NETWORK_HTTPSWIFIPROXY);
                }

                if (!(t instanceof ConnectTimeoutException
                        || t instanceof SocketTimeoutException
                        || t instanceof UnknownHostException)) {
                    centauriBusinessResultCode = ERROR_NETWORK_HTTPSCMWAP;
                    centauriBusinessResultMsg =
                            "The system is busy, please try again later"
                                    + getErrorCode(ERROR_NETWORK_HTTPSCMWAP);
                }

                t = t.getCause();
            }
        }
    }

    /**
     * Handling scenarios where Http connection is successful
     */
    private void handleSuccess(Response response) {
        // When the log_data data is reported successfully, the returned json string without the front and back braces
        // Cause the subsequent parsing of json data to fail, so if the front and back braces are missing,
        // please help add
        if (!TextUtils.isEmpty(response.responseBody)) {
            if (!response.responseBody.startsWith("{")) {
                if (!response.responseBody.endsWith("}")) {
                    response.responseBody = "{" + response.responseBody + "}";
                }
            }
        }

        JSONObject jsonObject;
        try { // In the case of success, responseBody cannot be empty
            jsonObject = new JSONObject(response.responseBody);
        } catch (JSONException e) { // If it is invalid Json data
            e.printStackTrace();

            // Construct the corresponding error message to facilitate the callback outside
            centauriBusinessResultCode = RESULT_CODE_ERROR_JSON;
            centauriBusinessResultMsg = RESULT_MSG_SYSTEM_BUSY;
            return;
        }

        final String ret = jsonObject.optString("ret");
        if (TextUtils.isEmpty(ret)) { // If there is no ret field in the Json data, or the ret field is empty
            // Construct the corresponding error message to facilitate the callback outside
            centauriBusinessResultCode = RESULT_CODE_JSON_RET_EMPTY;
            centauriBusinessResultMsg = RESULT_MSG_SYSTEM_BUSY;
            return;
        }

        try {
            centauriBusinessResultCode = Integer.parseInt(ret);
        } catch (NumberFormatException e) {
            e.printStackTrace();

            // Construct the corresponding error message to facilitate the callback outside
            centauriBusinessResultCode = RESULT_CODE_JSON_RET_NOT_NUM;
            centauriBusinessResultMsg = RESULT_MSG_SYSTEM_BUSY;
            return;
        }

        centauriBusinessResultMsg = jsonObject.optString("msg");
    }

    private static String generateFakeJson(final int resultCode, final String resultMsg) {
        return "{\"ret\": \"" + resultCode + "\", \"msg\":\"" + resultMsg + "\"}";
    }

    /**
     * Create a simulated network request result
     * Because the result of the network request is parsed by this class
     * So the code for creating simulated network request results should also be written in this category
     */
    static Response generateFakeCentauriResponse(final int resultCode, final String resultMsg) {
        // When the simulated network request result is parsed, it will be regarded as a successful Http request
        // but does not affect the analysis of error codes and error messages
        final Response response = new Response();
        response.exception = null;
        // In order to avoid generating a fake response when an Https error occurs,
        // the retry logic thinks that the fake response is successful, so I don’t try again
        // The responseCode of the fake response here is 100
        response.resultCode = 100;
        response.responseBody = generateFakeJson(resultCode, resultMsg);
        return response;
    }

    /**
     * Get the CentauriHttpResponse object built into Response
     * Every time Http request onStop, CentauriHttpResponseHandler will generate a CentauriHttpResponse object
     * Stored in Response through setTag
     *
     * @param response Response object returned by the underlying network module
     *                 The CentauriHttpResponse object in the @return Response object, if there is one,
     *                 otherwise it returns null
     */
    static CTIHttpResponse getCentauriHttpResponseFromResponse(final Response response) {
        if (response == null) {
            return null;
        }

        final Object tag = response.getTag();
        if (tag == null) {
            return null;
        }

        if (!(tag instanceof CTIHttpResponse)) {
            return null;
        }

        return (CTIHttpResponse) tag;
    }

    /**
     * Obtain the return code of Mi Master’s business logic from the Response returned by a low-level network module
     *
     * @param response Response returned by the underlying network request
     * @return Return code on the business logic of Master Mi
     */
    static int getCentauriBusinessResultCodeFromResponse(final Response response) {
        if (response != null) {
            if (response.isSuccess()) {
                JSONObject jsonObject;
                try {
                    jsonObject = new JSONObject(response.responseBody);
                    final String ret = jsonObject.optString("ret");
                    return Integer.parseInt(ret);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return -1;
    }

    /**
     * Determine whether the response returned by a low-level network module is a
     * logically successful network request from Master Mi's business
     *
     * @param response Response returned by the underlying network module
     * @return Is it a logically successful network request for Mi Master’s business?
     */
    static boolean isResponseCentauriBusinessSuccess(final Response response) {
        return response != null && getCentauriBusinessResultCodeFromResponse(response) == 0;

    }

    private static String getErrorCode(int error) {
        return "(100-100-" + error + ")";
    }
}
