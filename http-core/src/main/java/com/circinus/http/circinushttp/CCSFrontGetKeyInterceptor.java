package com.circinus.http.circinushttp;

import android.content.Context;
import android.text.TextUtils;

import com.circinus.comm.CTILog;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Before performing a normal network request
 * Check whether you need to change the key
 *
 * Used to discover in advance that the key needs to be changed before conducting
 * specific network services, and then insert a synchronous key-changing logic
 *
 * Created by a1w0n on 16/11/7.
 */
public class CCSFrontGetKeyInterceptor extends CTIGetKeyInterceptor {

    private static final String TAG = "FrontGetKey";

    // When the front get key logic cannot get the necessary parameters, in order to
    // prevent the subsequent logic from being confused because of the lack of these parameters
    // We directly intercept such requests here and directly call back to the outside
    private static final int ERROR_FRONT_GET_KEY_PARAM = 20007;

    private CTINetworkManager newNetworkManager;

    CTIFrontGetKeyInterceptor(final CTINetworkManager networkManager) {
        this.newNetworkManager = networkManager;
    }

    /**
     * Process the specific logic of whether to insert the get key before the request
     */
    @Override
    public Response intercept(final Request request, final Response previousResp) {
        if (request == null) {
            CTILog.e(TAG, "Null request, drop front get key!");
            return generateInterruptResponseWhenParamError();
        }

        // If it is not the network request of Master Mi, there is no need to change the key logic at all
        if (!(request instanceof CTIHttpRequest)) {
            CTILog.e(TAG, "Not circinus request, drop front get key!");
            return previousResp;
        }

        final CTIHttpRequest circinusHttpRequest = (CTIHttpRequest) request;

        // If the request specifies that the pre-replacement key logic is not required, skip it directly
        if (!circinusHttpRequest.needFrontGetKeyInterceptor) {
            CTILog.w(TAG, "Request set no need to front get key!");
            return previousResp;
        }

        // If there is no encryption parameter, there is no need to check whether to change the key
        if (!circinusHttpRequest.hasEncodeParameters()) {
            CTILog.d(TAG, "Current request has no encode parameter, drop front get key!");
            return previousResp;
        }

        if (newNetworkManager == null) {
            CTILog.e(TAG, "No network manager, drop front get key!");
            return generateInterruptResponseWhenParamError();
        }

        // If you cannot get the get key request object, you don’t need to replace
        // the key detection logic before processing.
        final CTIHttpRequest getKeyRequest = newNetworkManager.getGetKeyRequest(circinusHttpRequest);
        if (getKeyRequest == null) {
            CTILog.e(TAG, "No get key request, drop front get key!");
            return previousResp;
        }

        // If the current request itself is also a get key request, there is no need to insert get key logic
        if (newNetworkManager.isRequestInstanceAGetKeyRequest(request)) {
            CTILog.e(TAG, "Current request is get key request, drop front get key!");
            return previousResp;
        }

        final ICTICommonInfoGetter commonInfoGetter =
                newNetworkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            CTILog.e(TAG, "No comm info getter, drop front get key!");
            return generateInterruptResponseWhenParamError();
        }

        final Context context = newNetworkManager.getContext();
        if (context == null) {
            CTILog.e(TAG, "Null context, drop front get key!");
            return generateInterruptResponseWhenParamError();
        }

        final String offerID = circinusHttpRequest.getOfferIDFromRequest();
        if (TextUtils.isEmpty(offerID)) {
            CTILog.e(TAG, "Cannot get offer id from request for front get key process!");
            return generateInterruptResponseWhenParamError();
        }

        final String sdkVersion = commonInfoGetter.getSdkVersion();
        if (TextUtils.isEmpty(sdkVersion)) {
            CTILog.e(TAG, "Cannot get sdkVersion for front get key process!");
            return generateInterruptResponseWhenParamError();
        }

        final String openID = circinusHttpRequest.getOpenIDFromRequest();
        if (TextUtils.isEmpty(openID)) {
            CTILog.e(TAG, "Cannot get open id from request for front get key process!");
            return generateInterruptResponseWhenParamError();
        }

        // Check once more to prevent the need to get a synchronization lock before each check.
        // The writing is a bit like the DCL writing in singleton mode
        if (!newNetworkManager.needChangeKey(context, openID, offerID, sdkVersion)) {
            CTILog.d(TAG, "First need change key return false, drop front get key!");
            return previousResp;
        }

        CTILog.d(TAG, "First need change key return true!");

        // Here must be locked at the same time to detect whether to change the key logic and
        // execute the key change logic
        // So if two threads execute here concurrently, one thread changes the key,
        // and the other thread needsChangeKey will definitely be false
        // so that another thread will not change the key
        synchronized (GET_KEY_LOCK) {
            // To enter the synchronization block needs to be checked once, because it is possible
            // that two threads change the key at the same time, the first one is changed,
            // the second one comes in to check it,
            // and it will not be changed again
            if (!newNetworkManager.needChangeKey(context, openID, offerID, sdkVersion)) {
                CTILog.e(TAG, "Second need change key return false, drop front get key!");
                return previousResp;
            }

            CTILog.d(TAG, "Second need change key return true! Start get key!");

            final Response getKeyResponse = processGetKey(newNetworkManager, circinusHttpRequest);
            // What needs to be judged here is whether the network service is successful,
            // not whether the network request is successful
            // Business success must return the correct json string, and if the ret field inside is 0
            if (CTIHttpResponse.isResponseCircinusBusinessSuccess(getKeyResponse)) {
                CTILog.d(TAG, "Front get key request success!");
                newNetworkManager.notifyGetKeySuccess(getKeyResponse);
            } else {
                CTILog.e(TAG, "Front get key request fail!");
                // If this get key request fails and it is 1099 1094, the corresponding invalid key will be cleaned up
                clearKeyForRequestWhenGetKeyFail(newNetworkManager, getKeyResponse);

                // Interrupt subsequent Interceptor processing, and treat this result as the final
                // result of the entire Interceptor chain
                getKeyResponse.needBreakOtherInterceptors = true;
                return getKeyResponse;
            }
        }

        return previousResp;
    }

    /**
     * When the current get key logic finds that some necessary parameters are not available,
     * you can call this method to return the response
     * In this way, the request can be intercepted to prevent forgetting to pass these
     * parameters during the development phase
     */
    private static Response generateInterruptResponseWhenParamError() {
        CTILog.e(TAG, "Front get key fail!");

        final Response response = CTIHttpResponse.generateFakeCircinusResponse(
                ERROR_FRONT_GET_KEY_PARAM, "Internal parameter error!");

        response.needBreakOtherInterceptors = true;
        return response;
    }
}
