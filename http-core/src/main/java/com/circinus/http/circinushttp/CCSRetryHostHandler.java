package com.circinus.http.circinushttp;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Responsible for changing the ip or domain name of the request when retrying in the underlying Http library
 *
 * Created by a1w0n on 2017/2/9.
 */
class CTIRetryHostHandler extends CTIBaseHttpHandler {

    @Override
    public void onHttpStart(Request request) {
        super.onHttpStart(request);

        request.onHttpStart();
    }

    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        request.onHttpEnd(response);
    }

    /**
     * The last network request failed, this function will be called back before retrying
     * Here we can modify the requested host and replace it with another IP or domain name
     *
     * @param currentRetry is about to retry the first few times
     * @param maxRetry maximum number of retries
     * @param request The request that needs to be retried
     * @param response The last failed response
     */
    @Override
    public void onHttpRetry(int currentRetry, int maxRetry, Request request, Response response) {
        super.onHttpRetry(currentRetry, maxRetry, request, response);

        // If the request is empty, we can't do anything
        if (request == null) {
            return;
        }

        request.onHttpRetry(currentRetry, maxRetry, request, response);
    }
}
