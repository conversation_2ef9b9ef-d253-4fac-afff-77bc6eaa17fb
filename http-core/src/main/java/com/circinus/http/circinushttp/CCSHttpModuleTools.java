package com.circinus.http.circinushttp;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import com.circinus.comm.CTILog;

import java.security.cert.CertificateNotYetValidException;

import javax.security.cert.CertificateExpiredException;

//import com.circinus.circinus.comm.//HttpLog;

/**
 * Network module public Utils method class
 * Created by a1w0n on 2017/3/9.
 */
class CTIHttpModuleTools {

    // Is the current https request exception due to the wrong time?
    static boolean isTimeError(Throwable t) {
        //HttpLog.w("APBaseHttpReq", "isTimeError https exception" + t.toString());
        String sThrowable = t.toString();
        if (t instanceof CertificateExpiredException || t instanceof CertificateNotYetValidException) {
            return true;
        }

        if (TextUtils.isEmpty(sThrowable)) {
            return false;
        }

        // For this exception, the current time of the system is incorrect
        // javax.net.ssl.SSLHandshakeException: com.android.org.bouncycastle.jce.exception.ExtCertPathValidatorException:
        // Could not validate certificate: current time: Mon Jan 12 12:50:58 GMT+08:00 1970,
        // validation time: Fri Jan 01 08:00:00 GMT+08:00 2010
        if (sThrowable.contains("validation time") && sThrowable.contains("current time")) {
            return true;
        }

        // javax.net.ssl.SSLHandshakeException: com.android.org.bouncycastle.jce.exception.ExtCertPathValidatorException:
        // Could not validate certificate: Certificate not valid until Thu Oct 31 08:00:00 GMT+08:00 2013
        // (compared to Sun Aug 30 14:53:54 GMT+08:00 2009)
        if (sThrowable.contains("GMT") && sThrowable.contains("compared to")) {
            return true;
        }

        // If the certificate is still not verified, compare the client's time is earlier than our server's time
        if (sThrowable.contains("Could not validate certificate")) {
            final long certTime = 1451577600; // 20160101 Greenwich Mean Time
            long curretTime = System.currentTimeMillis() / 1000; // converted to seconds
            if (curretTime > 0 && certTime > curretTime) {
                return true;
            }
        }

        return false;
    }

    //Is wifi configured with proxy
    static boolean isWifiProxy(final Throwable t, final Context context) {
        if (context == null) {
            return false;
        }

        if (t == null) {
            return false;
        }

        String host = android.net.Proxy.getDefaultHost();
        // The current network type is wifi, and the proxy host is not empty
        if (getNetWorkType(context) == 1000 && !TextUtils.isEmpty(host)) {
            String sThrowable = t.toString();
            // For this exception, the current time of the system is incorrect
            // javax.net.ssl.SSLHandshakeException: java.security.cert.CertPathValidatorException:
            // Trust anchor for certification path not found.
            // Caused by: java.security.cert.CertificateException:
            // java.security.cert.CertPathValidatorException:
            // Trust anchor for certification path not found.
            if (sThrowable.contains("Trust anchor for certification path not found")) {
                return true;
            }
        }

        return false;
    }

    // Is it a problem caused by the sslv3 protocol
    private static boolean isSSLV3Error(Throwable t) {
        //HttpLog.w("APBaseHttpReq", "isTimeError https exception" + t.toString());
        String sThrowable = t.toString();

        //javax.net.ssl.SSLHandshakeException: javax.net.ssl.SSLProtocolException:
        // SSL handshake aborted: ssl=0x1fe7ed8: Failure in SSL library, usually a protocol error
        //error:1407743E:SSL routines:SSL23_GET_SERVER_HELLO:tlsv1 alert inappropriate fallback
        // (external/openssl/ssl/s23_clnt.c:661 0x402abcc3:0x00000000)
        return (sThrowable.contains("SSL handshake aborted") && sThrowable.contains("usually a protocol error"))
                || sThrowable.contains("GET_SERVER_HELLO");
    }

    /**
     * Get the current network type
     *
     * @param context judges the network type and needs to get android context
     * @return 0 = No network condition,1 = wap network,2 = 2g network,3 = 3g network,1000 = wifi network
     */
    private static int getNetWorkType(final Context context) {
        if (context == null) {
            CTILog.e("CTIHttpModuleTools", "getNetWorkType context null!");
            return 0;
        }

        int mNetWorkType = 0;

        try {
            // Determine if there is a network
            if (isNetworkConnect(context)) {
                //Subdivide if there is a network
                if (isNetworkWIFI(context)) { //wifi
                    mNetWorkType = 1000;
                } else if (isNetwork4G(context)) { //4g
                    mNetWorkType = 4;
                } else if (isNetwork3G(context)) { //3g
                    mNetWorkType = 3;
                } else if (isWAP(context)) { //wap
                    mNetWorkType = 1;
                } else {
                    mNetWorkType = 2;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return mNetWorkType;
    }

    /**
     * Determine whether the current network type is wap, such as cmwap, 3gwap, etc.
     *
     * @param context judges that this needs to be passed in an android context
     * @return true: it is wap,false: non-wap
     */
    private static boolean isWAP(final Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if ("MOBILE".equals(type)) {
                    String proxyHost = android.net.Proxy.getDefaultHost();
                    if (!TextUtils.isEmpty(proxyHost) &&
                            (proxyHost.contains("wap") || proxyHost.contains("WAP"))) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Determine whether the 3g network
     *
     * @param context determines whether the 3g network needs to pass in an android context
     * @return true: it is a 3g network,false: non-3g network
     */
    private static boolean isNetwork3G(final Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if ("MOBILE".equalsIgnoreCase(type)) {
                    TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(
                            Context.TELEPHONY_SERVICE);

                    switch (telephonyManager.getNetworkType()) {
                        case TelephonyManager.NETWORK_TYPE_EVDO_A:
                            return true; // ~ 600-1400 kbps

                        case TelephonyManager.NETWORK_TYPE_HSDPA:
                            return true; // ~ 2-14 Mbps

                        case TelephonyManager.NETWORK_TYPE_HSPA:
                            return true; // ~ 700-1700 kbps

                        case TelephonyManager.NETWORK_TYPE_HSUPA:
                            return true; // ~ 1-23 Mbps

                        case TelephonyManager.NETWORK_TYPE_UMTS:
                            return true; // ~ 400-7000 kbps

                        default:
                            return false;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * Determine whether 4g network
     *
     * @param context determines whether the 4g network needs to pass in an android context
     * @return true: it is a 4g network,false: non-4g network
     */
    private static boolean isNetwork4G(final Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                final String type = networkInfo.getTypeName();
                if ("MOBILE".equalsIgnoreCase(type)) {
                    TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(
                            Context.TELEPHONY_SERVICE);
                    switch (telephonyManager.getNetworkType()) {
                        case TelephonyManager.NETWORK_TYPE_LTE: //4g
                            return true; // ~ 10+ Mbps

                        default:
                            return false;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * Determine whether wifi network
     *
     * @param context determines whether the wifi network needs to pass in an android context
     * @return true: it is a wifi network,false: non-wifi network
     */
    private static boolean isNetworkWIFI(final Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                String type = networkInfo.getTypeName();
                if ("WIFI".equalsIgnoreCase(type)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * Determine if there is a network connection
     *
     * @param context judge whether there is a network connection and need to pass in an android context
     * @return true: there is a network connection,false: no network connection
     */
    private static boolean isNetworkConnect(final Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(
                    Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = manager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }
}
