package com.circinus.http.circinushttp;

import android.text.TextUtils;
import android.util.Patterns;

import com.circinus.comm.CTILog;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;
import com.circinus.http.utils.CTIIPValidator;

/**
 * Host must be set for every network request
 * Created by a1w0n on 16/11/25.
 */
final class CTIHostHeaderHandler extends CTIBaseHttpHandler {

    private static final String TAG = "CircinusHostHeader";

    private final CTINetworkManager newNetworkManager;

    // Is there any pollution to the request header of the request? If so,
    // clean up this pollution when the http end
    private final ThreadLocal<String> hasSetHostHeader = new ThreadLocal<String>() {
        @Override
        protected String initialValue() {
            return "";
        }
    };

    CTIHostHeaderHandler(final CTINetworkManager newNetworkManager) {
        this.newNetworkManager = newNetworkManager;
    }

    @Override
    public final void onHttpStart(Request request) {
        super.onHttpStart(request);

        if (request == null) {
            CTILog.e(TAG, "Set host header, request null");
            return;
        }

        // If it is not Mi Master’s own network request, there is no need to set http host logic at all
        if (!(request instanceof CTIHttpRequest)) {
            CTILog.e(TAG, "Set host header, not circinus request");
            return;
        }

        if (newNetworkManager == null) {
            CTILog.e(TAG, "Set host header, net manager null");
            return;
        }

        CTIHttpRequest circinusHttpRequest = (CTIHttpRequest) request;
        if (!circinusHttpRequest.needCircinusHostHeader) {
            CTILog.w(TAG, "Set host header, request set no need circinus header!");
            return;
        }

        ICTICommonInfoGetter commonInfoGetter = newNetworkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            CTILog.e(TAG, "Set host header, common info getter null");
            return;
        }

        final String host = commonInfoGetter.getHttpHostHeaderDomain(circinusHttpRequest);

        if (TextUtils.isEmpty(host)) {
            CTILog.e(TAG, "Set host header, got empty host");
            return;
        }

        // If it is an IP address, just ignore it
        if (CTIIPValidator.isIPAddress(host)) {
            CTILog.e(TAG, "Set host header, host is ip address = " + host);
            return;
        }

        // If it is not a webpage related domain name, just ignore it
        if (!Patterns.WEB_URL.matcher(host).matches()) {
            CTILog.e(TAG, "Set host header, host not web url = " + host);
            return;
        }

        request.addHttpHeader("Host", host);
        hasSetHostHeader.set(host);
        CTILog.d(TAG, "Set host = " + host);
    }

    @Override
    public final void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        final String host = hasSetHostHeader.get();

        // The Host header was polluted before, now let’s clean up this pollution
        if (!TextUtils.isEmpty(host)) {
            request.removeHttpHeader("Host", host);
            hasSetHostHeader.set("");
        }
    }
}
