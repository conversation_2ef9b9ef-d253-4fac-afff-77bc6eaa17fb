package com.circinus.http.circinushttp;

import android.text.TextUtils;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketException;
import java.nio.channels.SocketChannel;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.net.ssl.HandshakeCompletedListener;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

/**
 * Responsible for setting a custom Https certificate verifier before each Https request,
 * and restore it to the default Https certificate verifier after each Https request
 * Created by a1w0n on 2017/2/7.
 */
public class CTIHttpsCertHandler extends CTIBaseHttpHandler {

    // Https root certificate hard-coded by the client
    private final String certification;
    private final CTIIPChecker ipChecker;

    // Is there any pollution to the request header of the request? If so, clean up this pollution when the http end
    private ThreadLocal<Boolean> hasSetHttpsHeader = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    // Have you ever polluted the HostnameVerifier in the request? If so, the http end is,
    // this pollution should be cleared
    private ThreadLocal<Boolean> hasSetHostnameVerifier = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    // Have you ever polluted the SSLSocketFactory in the request? If so, the http end is,
    // this pollution should be cleared
    private ThreadLocal<Boolean> hasSetSSLSocketFactory = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };


    public CTIHttpsCertHandler(final String cert, final CTIIPChecker checker) {
        this.certification = cert;
        ipChecker = checker;
    }

    /**
     * Determine whether a request needs to set a custom Https certificate validator
     *
     * @param request a network request
     * @return Do you need to set up a custom Https certificate validator?
     */
    private boolean needCustomCert(final Request request) {
        if (request == null) {
            return false;
        }

        // Only Https requests need a custom certificate
        if (!request.isHttpsRequest()) {
            return false;
        }

        // If you are not using ip to access, you do not need to customize the certificate
        // Only in the case of using ip to access, it is possible to need a custom certificate
        // In Master Mi’s business, those who take the domain name do not need a custom certificate
        if (!request.isRequestWithIP()) {
            return false;
        }

        // Master Mi’s own network request only needs to set a custom certificate
        if (!(request instanceof CTIHttpRequest)) {
            return false;
        }

        // If the certificate verifier has been set in the request, it will not be executed
        if (request.getCustomHostnameVerifier() != null) {
            return false;
        }

        // If the certificate verifier has been set in the request, it will not be executed
        return request.getCustomSSLSocketFactory() == null;
    }

    @Override
    public void onHttpStart(Request request) {
        if (request == null) {
            return;
        }

        if (needCustomCert(request)) {
            if (!request.hasHttpHeader("https.protocols", "TLSv1")) {
                // Add header specific to Https request
                request.addHttpHeader("https.protocols", "TLSv1");
                hasSetHttpsHeader.set(true);
            }

            // Save the customized HostnameVerifier and SSLSocketFactory to the Request
            createSSLConnection(request);
        }
    }

    /**
     * There may be such a situation: the first two IPs failed,
     * the third time to change the domain name and try again
     * But when the IP is retried, the TLSv1 header is already stored in the Request
     * A custom certificate verifier is also set up, which pollutes the Request,
     * so every time the Http request ends, the related pollution should be cleared
     **/
    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        // If the Http request starts to pollute the request header, clear the pollution
        if (hasSetHttpsHeader.get()) {
            // Restore Defaults
            hasSetHttpsHeader.set(false);
            request.removeHttpHeader("https.protocols", "TLSv1");
        }

        if (hasSetHostnameVerifier.get()) {
            // Restore Defaults
            hasSetHostnameVerifier.set(false);
            request.clearCustomHostnameVerifier();
        }

        if (hasSetSSLSocketFactory.get()) {
            // Restore Defaults
            hasSetSSLSocketFactory.set(false);
            request.clearCustomSSLSocketFactory();
        }
    }

    private void createSSLConnection(final Request request) {
        try {
            SSLContext ctx = SSLContext.getInstance("TLSv1");
            // If it is ip, go to private certificate verification, if it is a domain name,
            // go to system certificate verification
            if (request.isRequestWithIP()) {
                //HttpLog.i(TAG, "ssl check init");
                ctx.init(null, new TrustManager[]{new MyTrustManager(certification)}, new SecureRandom());

                HostnameVerifier hostnameVerifier = new myHostnameVerifier(ipChecker);

                request.setCustomHostnameVerifier(hostnameVerifier);
                hasSetHostnameVerifier.set(true);

            } else {
                //HttpLog.i(TAG, "ssl system check init");
                ctx.init(null, null, new SecureRandom());
            }
            int code = android.os.Build.VERSION.SDK_INT;

            //HttpLog.i(TAG, "createSSLConnection sdk code:" + code);
            if (code >= 20) {
                request.setCustomSSLSocketFactory(ctx.getSocketFactory());
                hasSetSSLSocketFactory.set(true);
            } else {
                SSLSocketFactory NoSSLv3Factory = new APDelegateNoSSLv3Compat.NoSSLv3Factory(
                        ctx.getSocketFactory());
                request.setCustomSSLSocketFactory(NoSSLv3Factory);
                hasSetSSLSocketFactory.set(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //Manage the verification of X509 certificates against server and client certificates
    private static class MyTrustManager implements X509TrustManager {

        private X509TrustManager my509TrustManager;

        MyTrustManager(String cert) {
            if (TextUtils.isEmpty(cert)) {
                return;
            }

            try {
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                InputStream inputStream = new ByteArrayInputStream(cert.getBytes());
                X509Certificate certx = (X509Certificate) cf.generateCertificate(inputStream);
                inputStream.close();

                //Import the root certificate
                KeyStore.TrustedCertificateEntry skEntry = new KeyStore.TrustedCertificateEntry(certx);
                KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
                ks.load(null, null);
                ks.setEntry("ca_root", skEntry, null);

                //Load the certificate to the trust manager
                TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                tmf.init(ks);
                TrustManager tms[] = tmf.getTrustManagers();

                for (int i = 0; i < tms.length; i++) {
                    if (tms[i] instanceof X509TrustManager) {
                        my509TrustManager = (X509TrustManager) tms[i];
                        return;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void checkClientTrusted(X509Certificate[] xcs, String arg1)
                throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] xcs, String authType)
                throws CertificateException {
            //Use the root certificate to verify the private certificate of the server,
            //If the verification fails, a handshake failure exception will be thrown,
            // and the request will no longer go to the server side
            //HttpLog.i(TAG, "checkServerTrusted");
            my509TrustManager.checkServerTrusted(xcs, authType);
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return my509TrustManager.getAcceptedIssuers();
        }
    }

    private static class myHostnameVerifier implements HostnameVerifier {

        private final CTIIPChecker ipChecker;

        private myHostnameVerifier(CTIIPChecker checker) {
            ipChecker = checker;
        }

        @Override
        public boolean verify(String hostName, SSLSession session) {
            if (ipChecker == null) {
                return false;
            }

            return ipChecker.isCentauriIP(hostName);
        }

    }

    public interface CTIIPChecker {
        boolean isCentauriIP(String ip);
    }

    private static class APDelegateNoSSLv3Compat {
        /**
         * An {@link SSLSocket} that doesn't allow {@code SSLv3} only connections
         * <p>fixes https://github.com/koush/ion/issues/386</p>
         * <p>Android bug report: https://code.google.com/p/android/issues/detail?id=78187</p>
         */
        private static class NoSSLv3SSLSocket extends APDelegateSSLSocket {

            private NoSSLv3SSLSocket(SSLSocket delegate) {
                super(delegate);
                String canonicalName = delegate.getClass().getCanonicalName();
                if (!canonicalName.equals("org.apache.harmony.xnet.provider.jsse.OpenSSLSocketImpl")) {
                    // try replicate the code from HttpConnection.setupSecureSocket()
                    try {
                        Method msetUseSessionTickets = delegate.getClass().getMethod("setUseSessionTickets", boolean.class);
                        if (null != msetUseSessionTickets) {
                            msetUseSessionTickets.invoke(delegate, true);
                        }
                    } catch (NoSuchMethodException ignored) {
                        ignored.printStackTrace();
                    } catch (InvocationTargetException ignored) {
                        ignored.printStackTrace();
                    } catch (IllegalAccessException ignored) {
                        ignored.printStackTrace();
                    }
                }
            }

            @Override
            public void setEnabledProtocols(String[] protocols) {
                if (protocols != null && protocols.length == 1 && "SSLv3".equals(protocols[0])) {
                    // no way jose
                    // see issue https://code.google.com/p/android/issues/detail?id=78187
                    List<String> enabledProtocols = new ArrayList<String>(Arrays.asList(delegate.getEnabledProtocols()));
                    if (enabledProtocols.size() > 1) {
                        enabledProtocols.remove("SSLv3");
                    }
                    protocols = enabledProtocols.toArray(new String[enabledProtocols.size()]);
                }

                super.setEnabledProtocols(protocols);
            }
        }

        /**
         * {@link SSLSocketFactory} that doesn't allow {@code SSLv3} only connections
         */
        public static class NoSSLv3Factory extends SSLSocketFactory {
            private final SSLSocketFactory delegate;

            public NoSSLv3Factory(SSLSocketFactory factory) {
                this.delegate = factory;
            }

            private static Socket makeSocketSafe(Socket socket) {
                if (socket instanceof SSLSocket && !(socket instanceof NoSSLv3SSLSocket)) {
                    socket = new NoSSLv3SSLSocket((SSLSocket) socket);
                    String[] supportProtocol = ((SSLSocket) socket).getSupportedProtocols();
                    boolean isSupportV1_2 = false;
                    if (supportProtocol != null) {
                        for (int i = 0; i < supportProtocol.length; i++) {
                            if ("TLSv1.2".equals(supportProtocol[i])) {
                                isSupportV1_2 = true;
                            }
                        }
                    }
                    if (isSupportV1_2) {
                        ((SSLSocket) socket).setEnabledProtocols(new String[]{"TLSv1.1", "TLSv1.2"});
                    }
                }
                return socket;
            }

            @Override
            public String[] getDefaultCipherSuites() {
                return delegate.getDefaultCipherSuites();
            }

            @Override
            public String[] getSupportedCipherSuites() {
                return delegate.getSupportedCipherSuites();
            }

            @Override
            public Socket createSocket(Socket s, String host, int port, boolean autoClose) throws IOException {
                return makeSocketSafe(delegate.createSocket(s, host, port, autoClose));
            }

            @Override
            public Socket createSocket(String host, int port) throws IOException {
                return makeSocketSafe(delegate.createSocket(host, port));
            }

            @Override
            public Socket createSocket(String host, int port, InetAddress localHost, int localPort) throws IOException {
                return makeSocketSafe(delegate.createSocket(host, port, localHost, localPort));
            }

            @Override
            public Socket createSocket(InetAddress host, int port) throws IOException {
                return makeSocketSafe(delegate.createSocket(host, port));
            }

            @Override
            public Socket createSocket(InetAddress address, int port, InetAddress localAddress, int localPort) throws IOException {
                return makeSocketSafe(delegate.createSocket(address, port, localAddress, localPort));
            }
        }
    }

    private static class APDelegateSSLSocket extends SSLSocket {

        protected final SSLSocket delegate;

        APDelegateSSLSocket(SSLSocket delegate) {
            this.delegate = delegate;
        }

        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }

        @Override
        public String[] getEnabledCipherSuites() {
            return delegate.getEnabledCipherSuites();
        }

        @Override
        public void setEnabledCipherSuites(String[] suites) {
            delegate.setEnabledCipherSuites(suites);
        }

        @Override
        public String[] getSupportedProtocols() {
            return delegate.getSupportedProtocols();
        }

        @Override
        public String[] getEnabledProtocols() {
            return delegate.getEnabledProtocols();
        }

        @Override
        public void setEnabledProtocols(String[] protocols) {
            delegate.setEnabledProtocols(protocols);
        }

        @Override
        public SSLSession getSession() {
            return delegate.getSession();
        }

        @Override
        public void addHandshakeCompletedListener(HandshakeCompletedListener listener) {
            delegate.addHandshakeCompletedListener(listener);
        }

        @Override
        public void removeHandshakeCompletedListener(HandshakeCompletedListener listener) {
            delegate.removeHandshakeCompletedListener(listener);
        }

        @Override
        public void startHandshake() throws IOException {
            delegate.startHandshake();
        }

        @Override
        public boolean getUseClientMode() {
            return delegate.getUseClientMode();
        }

        @Override
        public void setUseClientMode(boolean mode) {
            delegate.setUseClientMode(mode);
        }

        @Override
        public boolean getNeedClientAuth() {
            return delegate.getNeedClientAuth();
        }

        @Override
        public void setNeedClientAuth(boolean need) {
            delegate.setNeedClientAuth(need);
        }

        @Override
        public boolean getWantClientAuth() {
            return delegate.getWantClientAuth();
        }

        @Override
        public void setWantClientAuth(boolean want) {
            delegate.setWantClientAuth(want);
        }

        @Override
        public boolean getEnableSessionCreation() {
            return delegate.getEnableSessionCreation();
        }

        @Override
        public void setEnableSessionCreation(boolean flag) {
            delegate.setEnableSessionCreation(flag);
        }

        @Override
        public void bind(SocketAddress localAddr) throws IOException {
            delegate.bind(localAddr);
        }

        @Override
        public synchronized void close() throws IOException {
            delegate.close();
        }

        @Override
        public void connect(SocketAddress remoteAddr) throws IOException {
            delegate.connect(remoteAddr);
        }

        @Override
        public void connect(SocketAddress remoteAddr, int timeout) throws IOException {
            delegate.connect(remoteAddr, timeout);
        }

        @Override
        public SocketChannel getChannel() {
            return delegate.getChannel();
        }

        @Override
        public InetAddress getInetAddress() {
            return delegate.getInetAddress();
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return delegate.getInputStream();
        }

        @Override
        public boolean getKeepAlive() throws SocketException {
            return delegate.getKeepAlive();
        }

        @Override
        public void setKeepAlive(boolean keepAlive) throws SocketException {
            delegate.setKeepAlive(keepAlive);
        }

        @Override
        public InetAddress getLocalAddress() {
            return delegate.getLocalAddress();
        }

        @Override
        public int getLocalPort() {
            return delegate.getLocalPort();
        }

        @Override
        public SocketAddress getLocalSocketAddress() {
            return delegate.getLocalSocketAddress();
        }

        @Override
        public boolean getOOBInline() throws SocketException {
            return delegate.getOOBInline();
        }

        @Override
        public void setOOBInline(boolean oobinline) throws SocketException {
            delegate.setOOBInline(oobinline);
        }

        @Override
        public OutputStream getOutputStream() throws IOException {
            return delegate.getOutputStream();
        }

        @Override
        public int getPort() {
            return delegate.getPort();
        }

        @Override
        public synchronized int getReceiveBufferSize() throws SocketException {
            return delegate.getReceiveBufferSize();
        }

        @Override
        public synchronized void setReceiveBufferSize(int size) throws SocketException {
            delegate.setReceiveBufferSize(size);
        }

        @Override
        public SocketAddress getRemoteSocketAddress() {
            return delegate.getRemoteSocketAddress();
        }

        @Override
        public boolean getReuseAddress() throws SocketException {
            return delegate.getReuseAddress();
        }

        @Override
        public void setReuseAddress(boolean reuse) throws SocketException {
            delegate.setReuseAddress(reuse);
        }

        @Override
        public synchronized int getSendBufferSize() throws SocketException {
            return delegate.getSendBufferSize();
        }

        @Override
        public synchronized void setSendBufferSize(int size) throws SocketException {
            delegate.setSendBufferSize(size);
        }

        @Override
        public int getSoLinger() throws SocketException {
            return delegate.getSoLinger();
        }

        @Override
        public synchronized int getSoTimeout() throws SocketException {
            return delegate.getSoTimeout();
        }

        @Override
        public synchronized void setSoTimeout(int timeout) throws SocketException {
            delegate.setSoTimeout(timeout);
        }

        @Override
        public boolean getTcpNoDelay() throws SocketException {
            return delegate.getTcpNoDelay();
        }

        @Override
        public void setTcpNoDelay(boolean on) throws SocketException {
            delegate.setTcpNoDelay(on);
        }

        @Override
        public int getTrafficClass() throws SocketException {
            return delegate.getTrafficClass();
        }

        @Override
        public void setTrafficClass(int value) throws SocketException {
            delegate.setTrafficClass(value);
        }

        @Override
        public boolean isBound() {
            return delegate.isBound();
        }

        @Override
        public boolean isClosed() {
            return delegate.isClosed();
        }

        @Override
        public boolean isConnected() {
            return delegate.isConnected();
        }

        @Override
        public boolean isInputShutdown() {
            return delegate.isInputShutdown();
        }

        @Override
        public boolean isOutputShutdown() {
            return delegate.isOutputShutdown();
        }

        @Override
        public void sendUrgentData(int value) throws IOException {
            delegate.sendUrgentData(value);
        }

        @Override
        public void setPerformancePreferences(int connectionTime, int latency, int bandwidth) {
            delegate.setPerformancePreferences(connectionTime, latency, bandwidth);
        }

        @Override
        public void setSoLinger(boolean on, int timeout) throws SocketException {
            delegate.setSoLinger(on, timeout);
        }

        @Override
        public void setSSLParameters(SSLParameters p) {
            delegate.setSSLParameters(p);
        }

        @Override
        public void shutdownInput() throws IOException {
            delegate.shutdownInput();
        }

        @Override
        public void shutdownOutput() throws IOException {
            delegate.shutdownOutput();
        }

        @Override
        public String toString() {
            return delegate.toString();
        }

        @Override
        public boolean equals(Object o) {
            return delegate.equals(o);
        }
    }
}
