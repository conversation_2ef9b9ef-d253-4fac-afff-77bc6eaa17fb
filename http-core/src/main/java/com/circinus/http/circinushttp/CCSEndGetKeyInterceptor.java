package com.circinus.http.circinushttp;

import android.text.TextUtils;

import com.circinus.http.core.Callback;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * After processing the normal network request, the server returns the scenario where the key needs to be changed
 * In this scenario, if the key change fails, the result returned to the caller is not the result
 * of the previous network request failure
 * It is the result of the failed key change request
 * Created by a1w0n on 16/11/24.
 */
class CTIEndGetKeyInterceptor extends CTIGetKeyInterceptor {

    private CTINetworkManager newNetworkManager;

    CTIEndGetKeyInterceptor(final CTINetworkManager networkManager) {
        this.newNetworkManager = networkManager;
    }

    @Override
    public synchronized Response intercept(final Request request, Response previousResp) {
        if (request == null) {
            return previousResp;
        }

        // If it is not the network request of <PERSON> <PERSON>, there is no need to change the key logic at all
        if (!(request instanceof CTIHttpRequest)) {
            return previousResp;
        }

        final CTIHttpRequest centauriHttpRequest = (CTIHttpRequest) request;
        if (!centauriHttpRequest.needEndGetKeyInterceptor) {
            return previousResp;
        }

        // If the newNetworkManager object is not available, the key change logic cannot be executed
        if (newNetworkManager == null) {
            return previousResp;
        }

        // If the current request is a get key request, there is no need for the post-key change logic
        if (newNetworkManager.isRequestInstanceAGetKeyRequest(request)) {
            return previousResp;
        }

        final CTIHttpRequest getKeyRequest = newNetworkManager.getGetKeyRequest(centauriHttpRequest);
        if (getKeyRequest == null) {
            return previousResp;
        }

        // If the previous request is successful on the network (responseCode == 200), get the ret field in its json
        // here previousResp == null is not relevant
        final int resultCode =
                CTIHttpResponse.getCentauriBusinessResultCodeFromResponse(previousResp);

        switch (resultCode) {
            // If the request is successful, but CGI detects that the key is about to expire in advance,
            // then need_change_key returns 1 to you, here only need
            // Change the key asynchronously, no need to re-execute the centauriRequest

            // There is no need to synchronize here, because the keys are replaced after AB at the same time,
            // and the two sets of keys that are changed are both usable. This has been confirmed with CGI
            case 0:
                if (responseHasNeedChangeKey(previousResp.responseBody)) {
                    // need_change_key == 1 to clear the cry key and key time to prevent the use of cry key
                    // to encrypt get key requests
                    clearCryptKeyAndKeyTimeForNeedChangeKey(newNetworkManager, getKeyRequest);

                    final Callback callback = new Callback() {
                        @Override
                        public void onResponse(Response response) {
                            if (CTIHttpResponse.isResponseCentauriBusinessSuccess(response)) {
                                newNetworkManager.notifyGetKeySuccess(response);
                            } else {
                                // If this get key request fails and it is 1094 1099, you need to clean up
                                // the corresponding key
                                clearKeyForRequestWhenGetKeyFail(newNetworkManager, response);
                            }
                        }
                    };

                    newNetworkManager.executeRequestAsyncWithNoCustomInterceptors(
                            getKeyRequest,
                            callback);
                }
                break;

            // If it is 1094 or 1099, 1105, you need to change the key and re-execute the centauriRequest
            case RET_SECKEYERROR:
            case RET_SECKEYVALID:
            case RET_DECRYPT_FAIL:
                clearKeyForRequestWhenGetKeyFail(newNetworkManager, request);
                return processChangeKeyAndNormalRequestAgain(
                        (CTIHttpRequest) request,
                        previousResp);

            default:
                break;
        }

        // If ret == 0, you can only return to previousResp even if you want to change the key,
        // otherwise the outside will find that the result cannot be parsed
        return previousResp;
    }

    /**
     * Process the key change logic, and if the key change is successful, re-execute the previous ordinary request
     * And no matter whether the re-execution of centauriHttpRequest is successful or not, the result will be returned
     * If the key change fails, the result of the failed key change will be returned
     */
    private Response processChangeKeyAndNormalRequestAgain(
            final CTIHttpRequest centauriHttpRequest,
            final Response previousResponse) {

        synchronized (GET_KEY_LOCK) {
            if (newNetworkManager == null) {
                return previousResponse;
            }

            final Response getKeyResponse = processGetKey(newNetworkManager, centauriHttpRequest);

            // What needs to be judged here is whether the network service is successful,
            // not whether the network request is successful
            // Business success must return the correct json string, and if the ret field inside is 0
            if (CTIHttpResponse.isResponseCentauriBusinessSuccess(getKeyResponse)) {
                newNetworkManager.notifyGetKeySuccess(getKeyResponse);

                // If the get key is successful, do a centauriHttpRequest request again
                final Response normalResponse =
                        newNetworkManager.executeRequestSyncWithNoCustomInterceptors(centauriHttpRequest);
                final int resultCode =
                        CTIHttpResponse.getCentauriBusinessResultCodeFromResponse(normalResponse);
                // If this centauriHttpRequest request is still 1094, 1099, 1105, clear the corresponding key
                if (resultCode == RET_SECKEYVALID || resultCode == RET_SECKEYERROR
                        || resultCode == RET_DECRYPT_FAIL) {
                    clearKeyForRequestWhenGetKeyFail(newNetworkManager, centauriHttpRequest);
                }

                // Re-execute the previous request and return the result after the re-execution,
                // regardless of success or failure
                return normalResponse;
            } else {
                // If the get key fails and it is 1094 1099, you need to clean up the related key
                clearKeyForRequestWhenGetKeyFail(newNetworkManager, getKeyResponse);

                // If the get key fails, return the result of the original network request instead
                // of the result of the failed get key
                return previousResponse;
            }
        }
    }

    private static boolean responseHasNeedChangeKey(final String response) {
        if (TextUtils.isEmpty(response)) {
            return false;
        }

        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(response);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (jsonObject == null) {
            return false;
        }

        final int needChangeKey = jsonObject.optInt("need_change_key");
        return needChangeKey == 1;
    }

}
