package com.circinus.http.circinushttp;

import android.content.Context;
import android.net.ConnectivityManager;

import com.circinus.http.core.Interceptor;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Interceptor used to determine network availability before starting to connect
 * If the network is impossible, a false network request result will be constructed
 *
 * Created by a1w0n on 16/11/28.
 */
public class CTINetAvailableInterceptor implements Interceptor {

    // The network is not connected
    private final static int ERROR_NETWORK_UNREACHABLE = 20006;

    private final CTINetworkManager newNetworkManager;

    CTINetAvailableInterceptor(final CTINetworkManager networkManager) {
        newNetworkManager = networkManager;
    }

    @Override
    public Response intercept(Request request, Response previousResp) {
        if (newNetworkManager == null) {
            return previousResp;
        }

        final Context context = newNetworkManager.getContext();
        if (context == null) {
            return previousResp;
        }

        // If there is no network connection, construct a response indicating that the network is not connected
        if (!getNetworkAvailable(context)) {
            // Construct a response indicating that the network is not connected
            Response fakeCentauriResponse = CTIHttpResponse.generateFakeCentauriResponse(
                    ERROR_NETWORK_UNREACHABLE, "The network is not connected, please check");

            // All other Interceptors after skipping
            fakeCentauriResponse.needBreakOtherInterceptors = true;

            return fakeCentauriResponse;
        }

        return previousResp;
    }

    private static boolean getNetworkAvailable(final Context context) {
        if (context == null) {
            return false;
        }

        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(
                Context.CONNECTIVITY_SERVICE);
        if (connectivity == null) {
            return false;
        } else {
            android.net.NetworkInfo info = connectivity.getActiveNetworkInfo();
            if (info != null) {
                return info.isAvailable();
            }
        }

        return false;
    }
}
