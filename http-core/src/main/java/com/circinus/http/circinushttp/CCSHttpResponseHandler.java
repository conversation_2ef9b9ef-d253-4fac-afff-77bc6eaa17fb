package com.circinus.http.circinushttp;

import android.content.Context;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Responsible for packaging the Response of the underlying network module into CircinusHttpResponse
 * In CircinusHttpResponse, some basic json fields are parsed and when errors occur
 * Pack some error messages
 *
 * Created by a1w0n on 16/11/28.
 */
public class CCSHttpResponseHandler extends CTIBaseHttpHandler {

    private CTINetworkManager newNetworkManager;

    public CTIHttpResponseHandler(final CTINetworkManager newNetworkManager) {
        this.newNetworkManager = newNetworkManager;
    }

    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        if (request == null) {
            return;
        }

        if (response == null) {
            return;
        }

        if (newNetworkManager == null) {
            return;
        }

        // If it is not Master Mi’s own network request, there is
        // no need to wrap Response into CircinusHttpResponse at all
        if (!(request instanceof CTIHttpRequest)) {
            return;
        }

        // There is no need to judge the context here, let the bottom layer judge it
        final Context context = newNetworkManager.getContext();

        // Wrap the Response and get the packaged result
        CTIHttpResponse circinusHttpResponse = new CTIHttpResponse(context, response);
        // Put the packaged result in the bottom Response
        response.setTag(circinusHttpResponse);
    }

}
