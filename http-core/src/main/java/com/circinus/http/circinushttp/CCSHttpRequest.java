package com.circinus.http.circinushttp;

import android.text.TextUtils;
import android.util.Log;

import com.circinus.http.core.Request;
import com.circinus.http.circinuskey.CTIToolAES;

import java.util.ArrayList;
import java.util.UUID;

/**
 * Encapsulation of Mi Master network request
 * Created by a1w0n on 16/10/25.
 */
public class CTIHttpRequest extends Request {
    private static final String TAG = "CTIHttpRequest";

    // Every Mi Master request has a unique identifier, which is used to identify this network request
    // This uuid is mainly used when reporting, the SDK itself does not need
    @SuppressWarnings("unused")
    public final String uuid = UUID.randomUUID().toString();

    // Parameters that need to be encrypted
    private final ArrayList<CTIHttpEncodeParameter> encodeParameters =
            new ArrayList<CTIHttpEncodeParameter>();

    // Save the key for encryption
    private CTIEncodeKey encodeKey;

    // If this flag bit is true, this request will be encrypted with base key
    boolean needUseBaseKeyToEncode = false;

    // Mark whether this request requires pre-key change logic, the default is true
    // At present, only the initialization request of the open version will set this flag,
    // because the initialization request of the open version and the key change request are the same CGI
    // The initialization request will definitely change back to the new key,
    // so it does not need to replace the key logic before
    @SuppressWarnings("WeakerAccess")
    protected boolean needFrontGetKeyInterceptor = true;

    // Mark whether this request requires post-key change logic, the default is true
    // At present, only the initialization request of the open version will set this flag,
    // because the initialization request of the open version and the key change request are the same CGI
    // The initialization request will definitely change back to the new key,
    // so it does not need post-replacement key logic
    // If the initialization fails, the post-replacement key will also fail
    @SuppressWarnings("WeakerAccess")
    protected boolean needEndGetKeyInterceptor = true;

    // Do you need to set the Host Header of Centauri
    protected boolean needCentauriHostHeader = true;

    public CTIHttpRequest() {
        // Set the Http headers to be added to Mi Master's network request
        addHttpHeader("Accept-Charset", "UTF-8");
        addHttpHeader("Content-Type", "application/x-www-form-urlencoded");
    }

    final CTIEncodeKey getEncodeKey() {
        return encodeKey;
    }

    @SuppressWarnings("unused")
    public final String getEncodeKeyString() {
        if (encodeKey == null) {
            return "";
        }

        return encodeKey.key;
    }

    /**
     * When this request is encrypted, this method will be called back
     * If the network module determines that this request does not need to be encrypted,
     * this method will not be called back
     */
    @SuppressWarnings({"unused", "WeakerAccess"})
    protected void onEncodeFinish() {
    }

    /**
     * This method will be called back when the request is ready to encrypt the
     * parameters to be encrypted
     * If the network module determines that this request does not need to be encrypted,
     * this method will not be called back
     */
    @SuppressWarnings({"unused", "WeakerAccess"})
    protected void onEncodeStart() {
    }

    /**
     * Get the openid parameter (if any) saved in this Request parameter
     */
    @SuppressWarnings("WeakerAccess")
    public final String getOpenIDFromRequest() {
        final String result = getParameter("openid");
        if (!TextUtils.isEmpty(result)) {
            return result;
        }

        return "";
    }

    /**
     * Get the offer id parameter (if any) saved in this Request parameter
     */
    @SuppressWarnings("WeakerAccess")
    public final String getOfferIDFromRequest() {
        // If you can get the offer id directly from the parameters, return directly
        final String result = getParameter("offer_id");
        if (!TextUtils.isEmpty(result)) {
            return result;
        }

        return "";
    }

    final boolean hasEncodeParameters() {
        return encodeParameters != null && encodeParameters.size() > 0;
    }

    /**
     * Get encryption parameters
     *
     * @param key encryption parameter key
     * @return If found, return the value of the parameter
     */
    @SuppressWarnings("unused")
    public String getEncodeParameter(final String key) {
        if (TextUtils.isEmpty(key)) {
            return "";
        }

        if (encodeParameters == null) {
            return "";
        }

        if (encodeParameters.size() == 0) {
            return "";
        }

        for (CTIHttpEncodeParameter parameter : encodeParameters) {
            if (key.equals(parameter.key)) {
                return parameter.value;
            }
        }

        // If not found, return an empty string directly
        return "";
    }

    /**
     * The mobile Q version may pass in a key-value pair parameter with an empty key.
     */
    @SuppressWarnings({"WeakerAccess", "unused"})
    public final void addHttpEncodeParameter(final String key, final String value) {
        // value can be empty, key may also be empty

        if (key == null) {
            return;
        }

        // The mobile Q version needs to add a string of parameters directly at the end of
        // the encrypted parameters, so we need to support the key to be an empty string
        // If it is an empty string, it does not check whether the same parameter already exists
        // If the current encryption parameter is empty, there is no need to check whether the
        // same parameter already exists
        // In other words, it currently supports the existence of multiple encryption parameters
        // with empty keys
        if (TextUtils.isEmpty(key) || encodeParameters.size() == 0) {
            // Why is this design here instead of using HashMap directly, please refer to the
            // description of the CentauriHttpEncodeParameter class declaration
            final CTIHttpEncodeParameter parameter = new CTIHttpEncodeParameter();
            parameter.key = key;
            parameter.value = value;

            encodeParameters.add(parameter);
            return;
        }

        // If the code goes here, it means that the key is definitely not an empty string,
        // we need to check whether the same key parameter already exists, and remove the duplicate

        for (CTIHttpEncodeParameter parameter : encodeParameters) {
            // If there is already a parameter with the same key, you only need to update its value
            if (key.equals(parameter.key)) {
                parameter.value = value;
                // After updating, you can return directly
                return;
            }
        }

        // The code comes here, indicating that there is no parameter with the same key,
        // we can add it directly

        final CTIHttpEncodeParameter parameter = new CTIHttpEncodeParameter();
        parameter.key = key;
        parameter.value = value;

        encodeParameters.add(parameter);
    }

    /**
     * Add encryption parameters to this Request
     * In some cases, the key time can be empty, so the keyTime parameter does not need to be empty
     *
     * @param centauriNetworkManager required CentauriNetworkManager object
     * @param encodeKey              What key is used to encrypt
     * @param keyTime                What key time is used to encrypt
     */
    @SuppressWarnings("WeakerAccess")
    public void doEncodeParameters(final CTINetworkManager centauriNetworkManager,
                                   final CTIEncodeKey encodeKey,
                                   final String keyTime) {
        if (encodeKey == null) {
            return;
        }

        // Get key request needs to be encrypted, but key time is not required
        // So the key time can be passed empty
        // Or when using secret key encryption, there is no key time

        // If this Request has no parameters that need to be encrypted, skip the encryption phase
        if (encodeParameters.isEmpty()) {
            return;
        }

        if (TextUtils.isEmpty(encodeKey.key)) {
            return;
        }

        // Save the encryption key
        this.encodeKey = encodeKey;

        // Notify the subclass that the encryption process is about to be executed
        onEncodeStart();

        final String str = encodeParametersListToString();

        if (TextUtils.isEmpty(str)) {
            return;
        }

        final String encodedString = CTIToolAES.encryptAES(str, encodeKey.getEKey(), centauriNetworkManager.getIV());

        ///////////////////////////////////////////////// ///////////////////
        // The encryption key cannot be printed here
        // HttpLog.d(TAG, "Encode current request with key = "+ encodeKey.key);
        ///////////////////////////////////////////////// ///////////////////

        addHttpParameters("encrypt_msg", encodedString);
        addHttpParameters("key_len", "newkey");
        // In the first GetKey, there is no key time, here is empty, and the key time parameter
        // is not passed at all, the result is the same
        // So there is no need here, if the keyTime is judged to be empty, the key_http-core-releasetime
        // parameter will not be passed
        // If you really can’t get the keyTime, it doesn’t matter if you pass the empty key_time parameter.
        // This logic has been confirmed with cgi students
        addHttpParameters("key_time", keyTime);
        // Here to pass the length before encryption
        addHttpParameters("msg_len", Integer.toString(str.length()));

        // Call back the notification method of the subclass to notify the end of the encryption process
        onEncodeFinish();
    }

    @SuppressWarnings("WeakerAccess")
    public boolean isEncodeWithBaseKey() {
        return encodeKey != null
                && !TextUtils.isEmpty(encodeKey.key)
                && ICTIEncodeKeyType.ENCODE_KEY_TYPE_BASE.equals(encodeKey.keyType);
    }

    @SuppressWarnings("WeakerAccess")
    public boolean isEncodeWithSecretKey() {
        return encodeKey != null
                && !TextUtils.isEmpty(encodeKey.key)
                && ICTIEncodeKeyType.ENCODE_KEY_TYPE_SECRET.equals(encodeKey.keyType);
    }

    @SuppressWarnings("WeakerAccess")
    public boolean isEncodeWithCryptKey() {
        return encodeKey != null
                && !TextUtils.isEmpty(encodeKey.key)
                && ICTIEncodeKeyType.ENCODE_KEY_TYPE_CRYPT.equals(encodeKey.keyType);
    }

    /**
     * Due to the mobile Q version, there is a requirement to directly add a piece of Http parameter passed
     * in from an external business at the end of the encrypted parameter, the format is unknown and uncontrollable
     * This is equivalent to the need to support the key to be empty when calling the addHttpEncodeParameter
     * method in the mobile Q version, and to directly insert it in consideration of the future
     * The number of external parameters at the end of the encryption parameter may be more than one, and
     * the HashMap that saves the encryption parameter does not support the same key of the two key-value pairs
     * So we make a transformation, encapsulate a CentauriHttpEncodeParameter to save the key-value pairs,
     * and then don't use HashMap to save the key-value pair list, but
     * Use ArrayList to save
     */
    private static class CTIHttpEncodeParameter {
        public String key;
        public String value;
    }

    @SuppressWarnings("WeakerAccess")
    protected String encodeParametersListToString() {
        if (encodeParameters == null || encodeParameters.size() <= 0) {
            return "";
        }

        final StringBuilder strBuff = new StringBuilder();

        for (CTIHttpEncodeParameter entry : encodeParameters) {
            if (!TextUtils.isEmpty(entry.key)) {
                strBuff.append(entry.key);
                strBuff.append("=");
            }

            strBuff.append(entry.value);
            strBuff.append("&");
        }

        if (strBuff.length() > 0) {
            strBuff.deleteCharAt(strBuff.length() - 1);
        }

        return strBuff.toString();
    }
}
