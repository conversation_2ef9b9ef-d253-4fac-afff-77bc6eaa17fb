package com.circinus.http.circinushttp;

import android.content.Context;
import android.text.TextUtils;

import com.circinus.http.core.Callback;
import com.circinus.http.core.HttpHandler;
import com.circinus.http.core.IHttpLog;
import com.circinus.http.core.Interceptor;
import com.circinus.http.core.NetworkManager;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;
import com.circinus.http.circinuskey.CTIKeyManager;
import com.circinus.http.utils.SPUtils;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * New network module manager
 * Created by a1w0n on 16/11/15.
 */
public class CTINetworkManager {

    private final NetworkManager networkManager;

    private final CTIKeyManager centauriKeyManager;

    private String customCert;
    private CTIHttpsCertHandler.CTIIPChecker ipChecker;
    private IAPConnectTimeoutGetter connectTimeoutGetter;
    private IAPReadTimeoutGetter readTimeoutGetter;
    private IAPIPMeasure iapipMeasure;
    private ICTIDataReportNotifier dataReportNotifier;

    private IAPGetKeyRequestGetter getKeyRequestGetter;
    private ICTIGetKeySuccessHandler getKeySuccessHandler;

    // Used in exchange for the general information of Master Mi, such as: openID, offerID
    private ICTICommonInfoGetter centauriCommonInfoGetter;

    // Record whether this class has called the setUp method
    // Repeat setUp is not allowed
    private AtomicBoolean hasSetUp = new AtomicBoolean(false);

    private Context context;

    private String baseKey;


    private String mIV;

    // You can set whether you need to use custom certificate related logic when accessing https ip
    // The default is true, because most of Mi Master’s SDKs need to set a custom certificate when accessing https ip
    private boolean needHttpsCustomCert = true;

    // Can the payment key be used to encrypt the get key request, the mainline version is not supported,
    // but the open version is supported
    // The open version needs to set this flag to true
    private boolean canUseCryptoKeyToEncodeGetKeyRequest = false;

    public CTINetworkManager(IHttpLog logInterface) {
        networkManager = new NetworkManager(logInterface);
        centauriKeyManager = new CTIKeyManager();
    }

    @SuppressWarnings("unused")
    public void setCustomCert(final String cert) {
        customCert = cert;
    }

    @SuppressWarnings("unused")
    public void setCentauriIPChecker(final CTIHttpsCertHandler.CTIIPChecker checker) {
        ipChecker = checker;
    }

    /**
     * Set whether the payment key can be used to encrypt the get key request, the mainline version is not supported,
     * but the open version is supported
     */
    @SuppressWarnings("unused")
    public void setCanUseCryptoKeyToEncodeGetKeyRequest(final boolean isCan) {
        canUseCryptoKeyToEncodeGetKeyRequest = isCan;
    }

    /**
     * Can the payment key be used to encrypt the get key request, the mainline version is not supported,
     * but the open version is supported
     */
    boolean isCanUseCryptoKeyToEncodeGetKeyRequest() {
        return canUseCryptoKeyToEncodeGetKeyRequest;
    }

    @SuppressWarnings("unused")
    public void setBaseKey(final String baseKey) {
        if (TextUtils.isEmpty(baseKey)) {
            return;
        }

        this.baseKey = baseKey;
    }

    /**
     * Set whether you need to use custom certificate related logic when accessing https ip
     */
    @SuppressWarnings("unused")
    public void setNeedHttpsCustomCert(final boolean need) {
        this.needHttpsCustomCert = need;
    }

    String getBaseKey() {
        return baseKey;
    }

    @SuppressWarnings("unused")
    public void cancelAllRequest() {
        networkManager.cancelAllRequest();
    }

    @SuppressWarnings("unused")
    public void cancelRequestByName(final String name) {
        networkManager.cancelRequestByName(name);
    }

    /**
     * setup
     */
    @SuppressWarnings("unused")
    public void setup() {
        if (!hasSetUp.compareAndSet(false, true)) {
            return;
        }

        // Set the maximum number of retries for each request
        // There is also a member that can be set in each Request. If it is also set in the Request,
        // the one in the Request will be used first
        networkManager.setDefaultMaxRetryTimes(2);

        // Add this Handler only if the need is set
        if (needHttpsCustomCert) {
            networkManager.addHttpHandler(new CTIHttpsCertHandler(customCert, ipChecker));
        }

        networkManager.addHttpHandler(new CTIHostHeaderHandler(this));
        networkManager.addHttpHandler(new CTITimeoutHandler(this));
        // Responsible for packaging the error message in the case of an exception in the Https request,
        // and saving the packaged result in the Response
        networkManager.addHttpHandler(new CTIHttpResponseHandler(this));
        // Time consuming reported
        networkManager.addHttpHandler(new CTIHttpTimeReportHandler(this));
        networkManager.addHttpHandler(new CTIIPMeasureHandler(this));
        networkManager.addHttpHandler(new CTIRetryHostHandler());
        networkManager.addHttpHandler(new CTIEncodeParameterHandler(this));

        // Processing the addition of different Interceptors, the order of addition is influential,
        // and the order of addition here cannot be changed arbitrarily
        {
            // Before starting to request the network, determine whether there is a network connection,
            // if not, return the constructed response directly
            networkManager.addFistInterceptor(new CTINetAvailableInterceptor(this));

            // Check whether the key needs to be changed before the request starts
            networkManager.addFistInterceptor(new CTIFrontGetKeyInterceptor(this));

            networkManager.addLastInterceptor(new CTIEndGetKeyInterceptor(this));

        }
        SPUtils.init(context);
        SPUtils.putString(SPUtils.ACP_MODEL, "AES/CBC/PKCS5Padding");
        SPUtils.putString(SPUtils.ALGORITHM_MODEL, "AES");
    }

    @SuppressWarnings("unused")
    public void addLastInterceptor(final Interceptor it) {
        networkManager.addLastInterceptor(it);
    }

    @SuppressWarnings("unused")
    public void addFistInterceptor(final Interceptor it) {
        networkManager.addFistInterceptor(it);
    }

    @SuppressWarnings("unused")
    public void addHttpHandler(final HttpHandler handler) {
        if (handler != null) {
            networkManager.addHttpHandler(handler);
        }
    }

    /**
     * Update two timeouts, because the mainline version timeouts are dynamically issued
     * So you need to update the latest timeout before each request
     */
    void updateConnectAndReadTimeout() {
        if (connectTimeoutGetter != null) {
            setDefaultConnectTimeout(connectTimeoutGetter.getConnectTimeout());
        }

        if (readTimeoutGetter != null) {
            setDefaultReadTimeout(readTimeoutGetter.getReadTimeOut());
        }
    }

    /**
     * Set the default connection timeout time, if it is not set in the Request, it will be used here
     *
     * @param connectTimeout The timeout you want to set
     */
    private void setDefaultConnectTimeout(final int connectTimeout) {
        // Prevent the timeout period from being too large, up to 20s
        if (connectTimeout > 0 && connectTimeout <= 20000) {
            networkManager.defaultConnectTimeout = connectTimeout;
        }
    }

    /**
     * Set the default connection timeout time, if it is not set in the Request, it will be used here
     *
     * @param readTimeout hope to set the timeout time
     */
    private void setDefaultReadTimeout(final int readTimeout) {
        // Prevent the timeout period from being too large, up to 20s
        if (readTimeout > 0 && readTimeout <= 20000) {
            networkManager.defaultReadTimeout = readTimeout;
        }
    }

    int getConnectTimeout() {
        if (connectTimeoutGetter != null) {
            return connectTimeoutGetter.getConnectTimeout();
        }

        return 0;
    }

    int getReadTimeout() {
        if (readTimeoutGetter != null) {
            return readTimeoutGetter.getReadTimeOut();
        }

        return 0;
    }

    void updateIPTimes(final String ip) {
        if (iapipMeasure != null) {
            iapipMeasure.updateIPTimes(ip);
        }
    }

    /**
     * Asynchronous execution of network requests, although synchronous network requests are supported,
     * but only the interface for asynchronous execution of requests is open
     *
     * @param request corresponding network request
     */
    @SuppressWarnings("unused")
    public void executeRequestAsync(final CTIHttpRequest request, final CTIHttpAns ans) {
        if (ans != null) {
            // Ans needs to get the key used by Request to encrypt parameters to decrypt
            ans.setCentauriHttpRequest(request);
        }

        networkManager.newCall(request).enqueue(ans);
    }

    /**
     * Execute network requests synchronously, ignoring all custom Interceptors,
     * but registered Handlers will still be executed
     * This interface is mainly used to change the key before and after the request.
     */
    @SuppressWarnings("WeakerAccess")
    public Response executeRequestSyncWithNoCustomInterceptors(final CTIHttpRequest request) {
        return networkManager.newCall(request).executeWithNoCustomInterceptor();
    }

    @SuppressWarnings("WeakerAccess")
    public Response executeRequestSyncWithAllCustomInterceptors(final CTIHttpRequest request) {
        return networkManager.newCall(request).executeWithAllCustomInterceptor();
    }

    /**
     * Perform asynchronous requests and ignore all custom Interceptors
     *
     * @param request  A request that needs to be executed asynchronously
     * @param callback Asynchronous request callback
     */
    void executeRequestAsyncWithNoCustomInterceptors(final CTIHttpRequest request,
                                                     final Callback callback) {
        if (request != null) {
            networkManager.newCall(request).enqueueWithNoCustomInterceptor(callback);
        }
    }

    @SuppressWarnings("unused")
    public void setConnectTimeoutGetter(IAPConnectTimeoutGetter connectTimeoutGetter) {
        this.connectTimeoutGetter = connectTimeoutGetter;
    }

    @SuppressWarnings("unused")
    public void setReadTimeoutGetter(IAPReadTimeoutGetter readTimeoutGetter) {
        this.readTimeoutGetter = readTimeoutGetter;
    }

    @SuppressWarnings("unused")
    public void setGetKeyRequestGetter(IAPGetKeyRequestGetter getKeyRequestGetter) {
        this.getKeyRequestGetter = getKeyRequestGetter;
    }

    /**
     * Obtain a get key request to perform the get key operation
     */
    CTIHttpRequest getGetKeyRequest(CTIHttpRequest request) {
        if (getKeyRequestGetter != null) {
            return getKeyRequestGetter.getGetKeyRequest(request);
        }

        return null;
    }

    @SuppressWarnings("WeakerAccess")
    public boolean isRequestInstanceAGetKeyRequest(final Request request) {
        if (request == null) {
            return false;
        }

        if (!(request instanceof CTIHttpRequest)) {
            return false;
        }

        CTIHttpRequest centauriHttpRequest = (CTIHttpRequest) request;

        final CTIHttpRequest getKeyRequest = getGetKeyRequest(centauriHttpRequest);
        if (getKeyRequest == null) {
            return false;
        }

        final String getKeyRequestClassName = getKeyRequest.getClass().getSimpleName();
        if (TextUtils.isEmpty(getKeyRequestClassName)) {
            return false;
        }

        final String thisClassName = request.getClass().getSimpleName();

        return !TextUtils.isEmpty(thisClassName) && thisClassName.equals(getKeyRequestClassName);
    }

    @SuppressWarnings("unused")
    public void setContext(Context context) {
        this.context = context;
    }

    Context getContext() {
        return context;
    }

    @SuppressWarnings("unused")
    public void setIPMeasure(IAPIPMeasure iapipMeasure) {
        this.iapipMeasure = iapipMeasure;
    }

    @SuppressWarnings("unused")
    public void setDataReportNotifier(ICTIDataReportNotifier dataReportNotifier) {
        this.dataReportNotifier = dataReportNotifier;
    }

    @SuppressWarnings("unused")
    public void setCentauriCommonInfoGetter(ICTICommonInfoGetter commonInfoGetter) {
        centauriCommonInfoGetter = commonInfoGetter;
    }

    @SuppressWarnings("WeakerAccess")
    public ICTICommonInfoGetter getCentauriCommonInfoGetter() {
        return centauriCommonInfoGetter;
    }

    void notifyNetworkSuccess(final Request request, final Response response) {
        if (dataReportNotifier != null) {
            dataReportNotifier.onNetworkSuccess(request, response);
        }
    }

    void notifyNetworkFailure(final Request request, final Response response) {
        if (dataReportNotifier != null) {
            dataReportNotifier.onNetworkFailure(request, response);
        }
    }

    @SuppressWarnings("unused")
    public void setGetKeySuccessHandler(ICTIGetKeySuccessHandler getKeySuccessHandler) {
        this.getKeySuccessHandler = getKeySuccessHandler;
    }

    /**
     * Notify the external key change successfully, and execute the related save and update logic
     *
     * @param response get key Response object obtained after success
     */
    void notifyGetKeySuccess(final Response response) {
        if (response == null) {
            return;
        }

        if (TextUtils.isEmpty(response.responseBody)) {
            return;
        }

        if (getKeySuccessHandler != null) {
            getKeySuccessHandler.onGetKeySuccess(response);
        }
    }

    @SuppressWarnings("WeakerAccess")
    public String getCryptKeyFromRam(final String openID, final String offerID) {
        return centauriKeyManager.getCryptoKeyFromRam(openID, offerID);
    }

    @SuppressWarnings("unused")
    public String readCryptKeyFromDisk(
            final Context context, final String openID, final String offerID, final String sdkVersion) {
        return centauriKeyManager.readCryptKeyFromDisk(context, openID, offerID, sdkVersion);
    }

    @SuppressWarnings("unused")
    public void setCryptKeyToRam(final String openID, final String offerID, final String cryptKey) {
        centauriKeyManager.setCryptKeyToRam(openID, offerID, cryptKey);
    }

    @SuppressWarnings("unused")
    public void saveCryptKeyToDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String strKey,
            final String sdkVersion) {
        centauriKeyManager.saveCryptKeyToDisk(context, openID, offerID, strKey, sdkVersion);
    }

    /**
     * Determine whether the key needs to be changed according to the specific open id and offer id
     *
     * @param context    needs to provide context
     * @param openID     corresponds to the open id
     * @param offerID    corresponding offer id
     * @param sdkVersion corresponds to the SDK version number
     * @return returns whether the key needs to be changed
     */
    public boolean needChangeKey(Context context, String openID, String offerID, String sdkVersion) {
        return centauriKeyManager.needChangeKey(context, openID, offerID, sdkVersion);
    }


    @SuppressWarnings("WeakerAccess")
    public void clearAllKey(Context context, String openId, String offerId, String sdkVersion) {
        centauriKeyManager.clearAllKey(context, openId, offerId, sdkVersion);
    }

    /**
     * When encrypting, use the payment key to encrypt, but report 1094/1099
     * It is best to clear the payment key and the matching keytime together to prevent the getkey
     * request from using the old one that has expired
     * key to encrypt, cause getkey itself to return 1094
     *
     * @param context    context
     * @param openID     clear the key corresponding to which openid
     * @param offerID    Clear the key corresponding to which offerID
     * @param sdkVersion Clear the key corresponding to which sdkVersion
     */
    @SuppressWarnings("WeakerAccess")
    public void clearCryptKeyAndKeyTime(final Context context,
                                        final String openID,
                                        final String offerID,
                                        final String sdkVersion) {
        centauriKeyManager.clearCryptKeyAndKeyTime(context, openID, offerID, sdkVersion);
    }

    /**
     * Read out the 3 keys stored in the disk and put them in ram
     *
     * @param context    needs to provide context
     * @param openID     corresponds to the open id
     * @param offerID    corresponding offer id
     * @param sdkVersion corresponds to the SDK version number
     */
    @SuppressWarnings("unused")
    public void loadKeyFromDisk(final Context context,
                                final String openID,
                                final String offerID,
                                final String sdkVersion) {
        centauriKeyManager.loadKeyFromDisk(context, openID, offerID, sdkVersion);
    }

    @SuppressWarnings("WeakerAccess")
    public String getSecretKeyFromRam(final String openID, final String offerID) {
        return centauriKeyManager.getSecretKeyFromRam(openID, offerID);
    }

    @SuppressWarnings("unused")
    public String readSecretKeyFromDisk(final Context context,
                                        final String openID,
                                        final String offerID,
                                        final String sdkVersion) {
        return centauriKeyManager.readSecretKeyFromDisk(context, openID, offerID, sdkVersion);
    }

    @SuppressWarnings("unused")
    public void setSecretKeyToRam(final String openID, final String offerID, final String secretKey) {
        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(secretKey)) {
            return;
        }

        centauriKeyManager.setSecretKeyToRam(openID, offerID, secretKey);
    }

    @SuppressWarnings("unused")
    public void saveSecretKeyToDisk(final Context context,
                                    final String openID,
                                    final String offerID,
                                    final String strKey,
                                    final String sdkVersion) {
        centauriKeyManager.saveSecretKeyToDisk(context, openID, offerID, strKey, sdkVersion);
    }

    @SuppressWarnings({"unused", "WeakerAccess"})
    public String getKeyTimeFromRam(final String openID, final String offerID) {
        return centauriKeyManager.getCryptKeyTimeFromRam(openID, offerID);
    }

    @SuppressWarnings("unused")
    public String readKeyTimeFromDisk(final Context context,
                                      final String openID,
                                      final String offerID,
                                      final String sdkVersion) {
        return centauriKeyManager.readCryptKeyTimeFromDisk(context, openID, offerID, sdkVersion);
    }

    @SuppressWarnings("unused")
    public void setKeyTimeToRam(final String openID, final String offerID, final String keyTime) {
        centauriKeyManager.setCryptKeyTimeToRam(openID, offerID, keyTime);
    }

    @SuppressWarnings("unused")
    public void saveKeyTimeToDisk(final Context context,
                                  final String openID,
                                  final String offerID,
                                  final String strKeyTime,
                                  final String sdkVersion) {
        centauriKeyManager.saveCryptKeyTimeToDisk(context, openID, offerID, strKeyTime, sdkVersion);
    }

    /**
     * Since the read timeout of the mainline version is issued dynamically, obtaining this timeout
     * time needs to rely on external modules, so we designed this interface to
     * Decoupling with external modules
     */
    public interface IAPReadTimeoutGetter {
        int getReadTimeOut();
    }

    /**
     * Since the connect timeout of the mainline version is issued dynamically, obtaining this timeout
     * time needs to rely on external modules, so we designed this interface to
     * Decoupling with external modules
     */
    public interface IAPConnectTimeoutGetter {
        int getConnectTimeout();
    }

    public interface IAPIPMeasure {
        void updateIPTimes(String ip);
    }

    /**
     * Used to get the external GetKeyRequest
     */
    public interface IAPGetKeyRequestGetter {
        CTIHttpRequest getGetKeyRequest(CTIHttpRequest request);
    }


    public String getIV() {
        return mIV;
    }

    public void setIV(String mIV) {
        this.mIV = mIV;
    }
}

