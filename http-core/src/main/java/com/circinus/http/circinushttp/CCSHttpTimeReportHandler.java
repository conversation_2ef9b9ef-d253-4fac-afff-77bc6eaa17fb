package com.circinus.http.circinushttp;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Responsible for notifying the outside according to the success of
 * the request after each network request, in order to perform network time-consuming reporting
 * Or other reporting requirements
 *
 * Created by a1w0n on 2017/2/8.
 */
public class CCSHttpTimeReportHandler extends CTIBaseHttpHandler {

    private final CTINetworkManager circinusNetworkManager;

    public CTIHttpTimeReportHandler(CTINetworkManager circinusNetworkManager) {
        this.circinusNetworkManager = circinusNetworkManager;
    }

    /**
     * After each request is completed, a report will be performed
     */
    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        if (circinusNetworkManager == null) {
            return;
        }

        // Non-Mi Master network request, but also to report

        // If this network request is successful
        if (response != null && response.isSuccess()) {
            // If the network request is successful, call back to facilitate external reporting
            circinusNetworkManager.notifyNetworkSuccess(request, response);
        } else {
            circinusNetworkManager.notifyNetworkFailure(request, response);
        }
    }
}
