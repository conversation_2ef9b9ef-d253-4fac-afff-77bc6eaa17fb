package com.circinus.http.circinushttp;

import android.content.Context;
import android.text.TextUtils;

import com.circinus.http.core.Interceptor;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * The base class of the two GetKeyInterceptor, the main function is to extract the common part of the two of them
 * Created by a1w0n on 16/11/25.
 */
class CTIGetKeyInterceptor implements Interceptor {

    static final Object GET_KEY_LOCK = new Object();

    // The security encryption key is invalid
    static final int RET_SECKEYERROR = 1094;
    // The security encryption key is invalid
    static final int RET_SECKEYVALID = 1099;
    // This is an error code unique to the get key request, indicating that the get key request decryption failed
    static final int RET_DECRYPT_FAIL = 1105;

    /**
     * Handle the key change logic
     */
    Response processGetKey(
            final CTINetworkManager newNetworkManager, CTIHttpRequest circinusHttpRequest) {
        // Build a Response for use
        final Response httpResponse = new Response();

        if (newNetworkManager == null) {
            return httpResponse;
        }

        final CTIHttpRequest getKeyRequest = newNetworkManager.getGetKeyRequest(circinusHttpRequest);
        if (getKeyRequest == null) {
            return httpResponse;
        }

        final Response getKeyResponse =
                newNetworkManager.executeRequestSyncWithNoCustomInterceptors(getKeyRequest);

        if (CTIHttpResponse.isResponseCircinusBusinessSuccess(getKeyResponse)) {
            return getKeyResponse;
        }

        // If the first get key request fails, then another get key request,
        // this time we directly use the base key to encrypt the get key request
        final CTIHttpRequest getKeyRequest2 = newNetworkManager.getGetKeyRequest(circinusHttpRequest);
        if (getKeyRequest2 == null) {
            return httpResponse;
        }

        // We directly specify this time to change the key to use the base key to encrypt
        getKeyRequest2.needUseBaseKeyToEncode = true;

        return newNetworkManager.executeRequestSyncWithNoCustomInterceptors(getKeyRequest2);
    }

    @Override
    public Response intercept(Request request, Response previousResp) {
        return null;
    }

    /**
     * If you report 1094 1099, you need to clear the corresponding key saved in
     * the memory and disk to prevent the get key request from being used
     * The expired key is used to encrypt the get key request, causing the get key request to fail
     * Or
     * Prevent subsequent requests from still using the expired Key
     * The getKeyResponse here is used to determine whether this get key request is 1094 1099,
     * and if so, do the corresponding cleanup work
     *
     * @param networkManager needs CircinusNetworkManager object
     * @param getKeyResponse The response object of the get key request required
     */
    static void clearKeyForRequestWhenGetKeyFail(final CTINetworkManager networkManager,
                                                 final Response getKeyResponse) {
        if (networkManager == null) {
            return;
        }

        if (getKeyResponse == null) {
            return;
        }

        final Request request = getKeyResponse.request();
        if (request == null) {
            return;
        }

        // If it is not the response of the get key request, there is no need to clean up the related key here
        if (!networkManager.isRequestInstanceAGetKeyRequest(request)) {
            return;
        }

        final int resultCode =
                CTIHttpResponse.getCircinusBusinessResultCodeFromResponse(getKeyResponse);

        if (resultCode == RET_SECKEYVALID || resultCode == RET_SECKEYERROR ||
                resultCode == RET_DECRYPT_FAIL) {
            clearKeyForRequestWhenGetKeyFail(networkManager, request);
        }
    }

    /**
     * If you report 1094 1099, you need to clear the key saved in the memory and disk to
     * prevent the getkey request from being used
     * The expired key is used to encrypt the getkey request, causing the getkey request to fail
     * Or
     * Prevent subsequent requests from still using the expired Key
     *
     * @param networkManager needs CircinusNetworkManager object
     * @param request        Request object required
     */
    static void clearKeyForRequestWhenGetKeyFail(final CTINetworkManager networkManager,
                                                 final Request request) {

        if (networkManager == null) {
            return;
        }

        if (request == null) {
            return;
        }

        // If it is not the network request of Master Mi, there is no need to clear the key at all
        if (!(request instanceof CTIHttpRequest)) {
            return;
        }

        final CTIHttpRequest circinusHttpRequest = (CTIHttpRequest) request;

        final Context context = networkManager.getContext();
        if (context == null) {
            return;
        }

        final ICTICommonInfoGetter commonInfoGetter = networkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            return;
        }

        final String sdkVersion = commonInfoGetter.getSdkVersion();
        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        final String offerId = circinusHttpRequest.getOfferIDFromRequest();
        if (TextUtils.isEmpty(offerId)) {
            return;
        }

        final String openId = circinusHttpRequest.getOpenIDFromRequest();
        if (TextUtils.isEmpty(openId)) {
            return;
        }

        if (circinusHttpRequest.isEncodeWithSecretKey()) {
            networkManager.clearAllKey(context, openId, offerId, sdkVersion);
        } else if (circinusHttpRequest.isEncodeWithCryptKey()) {
            networkManager.clearCryptKeyAndKeyTime(context, openId, offerId, sdkVersion);
        }
    }

    /**
     * If CGI, need_change_key == 1 is returned to you, you change the key at this time,
     * but the get key request uses the cry key to encrypt
     * Although the cry key has not expired, CGI does not support this
     * So if you find that need_change_key == 1, you need to clear the cry key and key time
     * immediately, so that the get key request will not
     * Encrypted with cry key
     */
    static void clearCryptKeyAndKeyTimeForNeedChangeKey(final CTINetworkManager networkManager,
                                                        final CTIHttpRequest request) {
        if (networkManager == null) {
            return;
        }

        if (request == null) {
            return;
        }

        final Context context = networkManager.getContext();
        if (context == null) {
            return;
        }

        final ICTICommonInfoGetter commonInfoGetter = networkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            return;
        }

        final String sdkVersion = commonInfoGetter.getSdkVersion();
        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        final String offerId = request.getOfferIDFromRequest();
        if (TextUtils.isEmpty(offerId)) {
            return;
        }

        final String openId = request.getOpenIDFromRequest();
        if (TextUtils.isEmpty(openId)) {
            return;
        }

        networkManager.clearCryptKeyAndKeyTime(context, openId, offerId, sdkVersion);
    }


}
