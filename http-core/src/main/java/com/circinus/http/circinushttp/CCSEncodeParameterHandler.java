package com.circinus.http.circinushttp;

import android.text.TextUtils;

import com.circinus.http.core.Request;

/**
 * Responsible for getting the key needed for encryption before starting each request,
 * and then encrypting some of the parameters
 * Then add the encrypted result to http parameters
 * Created by a1w0n on 2017/3/24.
 */
public final class CTIEncodeParameterHandler extends CTIBaseHttpHandler {

    private final CTINetworkManager networkManager;

    @SuppressWarnings("WeakerAccess")
    public CTIEncodeParameterHandler(final CTINetworkManager networkManager) {
        this.networkManager = networkManager;
    }

    @Override
    public void onHttpStart(Request request) {
        super.onHttpStart(request);

        handleEncodeParameters(request);
    }

    /**
     * Get the current key and keyTime, encrypt the parameters that need to be encrypted in the request
     */
    private void handleEncodeParameters(final Request request) {
        if (request == null) {
            return;
        }

        // Not requested by <PERSON>, no need to add encryption parameters for him
        if (!(request instanceof CTIHttpRequest)) {
            return;
        }

        if (networkManager == null) {
            return;
        }

        final CTIHttpRequest circinusHttpRequest = (CTIHttpRequest) request;

        // If there are no parameters that need to be encrypted
        if (!circinusHttpRequest.hasEncodeParameters()) {
            return;
        }

        // no key time is also possible
        // For example, when you get the key for the first time, there is no key time
        // Or when using secret key encryption, there is no key time
        final String keyTime = tryGetEncodeKeyTime(circinusHttpRequest);
        final CTIEncodeKey encodeKey = tryGetEncodeKey(circinusHttpRequest);

        // If the key is empty, it cannot be encrypted, just return
        if (TextUtils.isEmpty(encodeKey.key)) {
            return;
        }

        circinusHttpRequest.doEncodeParameters(networkManager, encodeKey, keyTime);
    }

    /**
     * Get the crypt key to encrypt, if you can't get it, try to encrypt with the secret key,
     * if you still can't get it, try to encrypt with the base key
     * The case of using base key to encrypt, generally only occurs when
     * there is no key in the disk to make a get key request.
     */
    private CTIEncodeKey tryGetEncodeKey(final CTIHttpRequest request) {
        final CTIEncodeKey encodeKey = new CTIEncodeKey();

        if (networkManager == null) {
            return encodeKey;
        }

        if (request == null) {
            return encodeKey;
        }

        final String baseKey = networkManager.getBaseKey();
        encodeKey.keyType = ICTIEncodeKeyType.ENCODE_KEY_TYPE_BASE;
        encodeKey.setEKey(baseKey);

        // If the request is directly encrypted with the base key through the mark bit request,
        // you can directly return it here
        if (request.needUseBaseKeyToEncode) {
            return encodeKey;
        }

        final ICTICommonInfoGetter commonInfoGetter = networkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            return encodeKey;
        }

        // Take the openid directly from the request to get the key, no need to consider the logic of the payID
        // If you really want to use the payID, the openid in the request will also be assigned to the payID
        // So it's definitely correct to get the key directly from the openid in the request
        final String openID = request.getOpenIDFromRequest();
        if (TextUtils.isEmpty(openID)) {
            return encodeKey;
        }

        final boolean isCanUseCryptoKeyToEncodeGetKeyRequest =
                networkManager.isCanUseCryptoKeyToEncodeGetKeyRequest();

        final String currentOfferID = request.getOfferIDFromRequest();
        if (TextUtils.isEmpty(currentOfferID)) {
            return encodeKey;
        }

        final String secretKey = networkManager.getSecretKeyFromRam(openID, currentOfferID);
        if (!TextUtils.isEmpty(secretKey)) {
            encodeKey.keyType = ICTIEncodeKeyType.ENCODE_KEY_TYPE_SECRET;
            encodeKey.setEKey(secretKey);
        }

        final String cryptKey = networkManager.getCryptKeyFromRam(openID, currentOfferID);

        // If you can use the payment key to encrypt the get key request (such as the open version)
        // You can use the payment key directly, it's ok if it is not empty
        if (isCanUseCryptoKeyToEncodeGetKeyRequest) {
            if (!TextUtils.isEmpty(cryptKey)) {
                encodeKey.keyType = ICTIEncodeKeyType.ENCODE_KEY_TYPE_CRYPT;
                encodeKey.setEKey(cryptKey);
            }
        } else {
            // If the payment key cannot be used to encrypt the get key request
            // (such as other versions except the open version)
            // You must determine whether the current request is a get key request,
            // and only when it is not a get key request and is not empty can you use the
            // payment key to encrypt
            final boolean isCurrentRequestAGetKeyRequest =
                    networkManager.isRequestInstanceAGetKeyRequest(request);
            if (!TextUtils.isEmpty(cryptKey) && !isCurrentRequestAGetKeyRequest) {
                encodeKey.keyType = ICTIEncodeKeyType.ENCODE_KEY_TYPE_CRYPT;
                encodeKey.setEKey(cryptKey);
            }
        }

        return encodeKey;
    }

    /**
     * Try to get the key time required for this encryption, the return value is empty,
     * it will happen under all normal circumstances
     * For example: just install the apk where the SDK is located, there is no key time when
     * the first get key is obtained
     *
     * @param request needs to get the correct openid from the request
     * @return key time or empty string
     */
    private String tryGetEncodeKeyTime(CTIHttpRequest request) {
        if (networkManager == null) {
            return "";
        }

        if (request == null) {
            return "";
        }

        final ICTICommonInfoGetter commonInfoGetter = networkManager.getCircinusCommonInfoGetter();
        if (commonInfoGetter == null) {
            return "";
        }

        // Take the openid directly from the request to get the key, no need to consider the logic of the payID
        // If you really want to use the payID, the openid in the request will also be assigned to the payID
        // So it's definitely correct to get the key directly from the openid in the request
        final String openID = request.getOpenIDFromRequest();
        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        final String currentOfferID = request.getOfferIDFromRequest();
        if (TextUtils.isEmpty(currentOfferID)) {
            return "";
        }

        final String keyTime = networkManager.getKeyTimeFromRam(openID, currentOfferID);
        if (TextUtils.isEmpty(keyTime)) {
            return "";
        } else {
            return keyTime;
        }
    }
}


