apply plugin: 'com.android.library'
apply plugin: 'maven-publish'
//发布到maven仓库脚本
apply from: './mavenUpload.gradle'

//2020-10-23 11:36:51 by dongbingliu
//由于升级了Gradle版本, 旧的发布到Maven仓库有问题, 发布到Maven仓库使用Gradle -> upload -> uploadArchives

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30
        versionCode 20105
        versionName "2.01.05"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }



    /**
     * build aar and rename aar by dongbingliu 2020-10-30 星期五
     */
    setVersion(defaultConfig.versionName)
    libraryVariants.all { variant ->
        if (variant.buildType.name == 'release') {
            variant.outputs.all {
                outputFileName = "circinus-http-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}.aar"
            }
        }else{
            variant.outputs.all {
                outputFileName = "circinus-http-core-${getVersion()}-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-debug.aar"
            }
        }
    }
//    android.packageBuildConfig = false

    lintOptions {
        abortOnError false
    }
}

/**
 * build jar and rename jar by dongbingliu 2020-10-30 星期五
 */
task ccsHttpSourceJar(type: Copy) {
    String jarPackageName = "circinus-https-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}-${getVersion()}.jar"

    //删除存在的
    delete("build/libs")
    String libDir = file('build/libs')
    //设置拷贝的文件来源
//    from('build/intermediates/packaged-classes/release/classes.jar')
    from('build/intermediates/compile_library_classes_jar/release/classes.jar')
    ////新生成的jar包的目录
    into(libDir)
    //将新生成的jar包classes.jar(新生成的jar文件名默认为classes.jar)放入上面的目录下目录下
    include('classes.jar')
    //重命名成我们设定的名字
    rename ('classes.jar', jarPackageName)
}
ccsHttpSourceJar.dependsOn(build)


// Add a new configuration to hold your dependencies
configurations {
    myConfig
}
dependencies {
    implementation files('libs/circinus-log-2.01.01-20210701-419eebd.jar')
    compile fileTree(include: ['*.jar'], dir: 'libs')
//    implementation  'com.circinus.comm:circinuscommon-log:2.0.3'
//    myConfig 'com.circinus.comm:circinuscommon-log:2.0.3'
}

task sourcesJar(type: Jar, dependsOn: "assembleRelease") {


    //http-core class源码路径
    from "build/intermediates/javac/release/classes"

    //依赖库路径
    from {configurations.myConfig.collect {it.isDirectory() ? it : zipTree(it) }}


    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
    exclude "**/BuildConfig.class"
    exclude('**/BuildConfig.class')

}

group 'com.circinus.comm'
version "${android.defaultConfig.versionName}"    //-SNAPSHOT

publishing {
    repositories {
//        maven {
//            //发布到本地
//            url project.uri("${project.buildDir.absolutePath}\\maven");
//        }

        maven {
            //发布到腾讯内部仓库-snapshots，非release仓库，同一个版本号可以upload多次
            //需要版本号加上后缀：'-SNAPSHOT' 才能生效,  比如 '1.3.2-SNAPSHOT'
            credentials {
                username 'g_midas'
                password 'aca5de342b7e11eab9396c92bf5e3645'
            }
            url project.uri("https://mirrors.tencent.com/repository/maven/thirdparty/")
        }
    }

    publications {
        http(MavenPublication) {
            artifactId 'circinuscommon-http'
            artifact sourcesJar
            //The publication doesn't know about our dependencies, so we have to manually add them to the pom
            pom.withXml {
                def dependenciesNode = asNode().appendNode('dependencies')

                //Iterate over the compile dependencies (we don't want the test ones), adding a <dependency> node for each
                configurations.compile.allDependencies.each {
                    if (it instanceof ExternalModuleDependency) {
                        def dependencyNode = dependenciesNode.appendNode('dependency')
                        dependencyNode.appendNode('groupId', it.group)
                        dependencyNode.appendNode('artifactId', it.name)
                        dependencyNode.appendNode('version', it.version)
                    }
                }
            }
        }
    }
}
task ccsCoresJar(type: Jar, dependsOn: "assembleRelease") {
    //jar package name
    archivesBaseName = "circinus-http-${rootProject.getReleaseTime()}-${rootProject.getGitRevision()}"
    //路径会跟随gradle版本变更而变化
    from "build/intermediates/javac/release/classes"
    include('com/')

    //依赖的maven仓库包打进一个jar包
//    from {configurations.myConfig.collect { it.isDirectory() ? it : zipTree(it) }}

    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
    exclude('**/BuildConfig.class')

    // copy 至ccsLibs目录,build/libs目录是中间目录,编译时候自动删除不可控
    copy {
        //删除存在的
//        delete("build/ccsLibs")
        String libDir = file('build/ccsLibs')
        from("build/libs")
        into("build/ccsLibs")
    }
}
task buildJar(dependsOn: ['compileReleaseJavaWithJavac'], type: Jar) {
    appendix = "sample"
    baseName = "demo"
    version = "1.0.0"
    classifier = "release"
    // 后缀名
    extension = "jar"

    // 最终的 Jar 包名，如果没设置，默认为 [baseName]-[appendix]-[version]-[classifier].[extension]
    // archiveName = "AndroidDemo.jar"
    // 需打包的资源所在的路径集,即使classes文件夹不存在，也会自动生成
    def srcClassDir = [project.buildDir.absolutePath + "/intermediates/classes/release"]

    // 初始化资源路径集
    from srcClassDir
    // 可以继续包含其他jar包
    //from (project.zipTree("libs/第三方库包.jar"))

    // 如果没有设置，默认放到build/libs
    destinationDir = file("build/libsbb")

    // 去除路径集下部分的资源
    exclude "**/**/BuildConfig.class"
    exclude "**/**/BuildConfig\$*.class"
    exclude "**/R.class"
    exclude "**/R\$*.class"

    // 只导入资源路径集下的部分资源
    include "**/*.class"
}



