# Circinus公共组件---网络模块


CircinusCommon-Http: 提供网络相关功能

## Project Structure




## Develop

使用Android Studio 2.2及其以上版本进行开发。

导入到Android Studio

* 方式1:
  * 使用`git clone http://git.code.oa.com/MidasSDK/midas_for_qq.git `命令克隆到本地。
  * 使用Android Studio 打开此项目，并在弹提示框后选择不更新Gradle。
  * 点击工具栏的Sync Project同步Gradle。
* 方式2:
  * Android Studio 菜单栏：File---New---Project from Version Control---Git。
  * 填入Url等信息，点确定。同样在弹提示框时选择不更新Gradle。



## Build & Run

#### 运行Demo

​	菜单栏---Run---Run 'app'     或者    Shift+F10



## Documentation

待更新



## License

闭源项目。









