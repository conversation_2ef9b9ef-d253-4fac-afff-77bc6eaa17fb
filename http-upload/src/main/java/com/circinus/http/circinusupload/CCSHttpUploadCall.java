package com.circinus.http.circinusupload;

import com.circinus.http.core.Call;
import com.circinus.http.core.Callback;
import com.circinus.http.core.Dispatcher;
import com.circinus.http.core.ExecutableCall;
import com.circinus.http.core.Interceptor;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

import java.util.ArrayList;

/**
 * Created by cheneyang on 2017/10/31.
 */

class CTIHttpUploadCall implements Call {

    // Http请求对应的Request
    private final Request request;
    private CTIUploadNetworkManager networkManager;

    private boolean executed = false;
    private boolean canceled = false;

    CTIHttpUploadCall(Request request, CTIUploadNetworkManager networkManager) {
        this.request = request;
        this.networkManager = networkManager;
    }

    @Override
    public void cancel() {
        if (request != null) {
            // 取消任务，取消后，不会回调外部
            request.stopRequest();
        }
        canceled = true;
    }

    @Override
    public void enqueue(Callback callback) {
        if (networkManager == null) {
            return;
        }

        final Dispatcher dispatcher = networkManager.dispatcher();
        if (dispatcher == null) {
            return;
        }
        AsyncHttpUploadCall call = new AsyncHttpUploadCall(callback, false);
        dispatcher.enqueue(call);
        executed = true;
    }

    @Override
    public void enqueueWithNoCustomInterceptor(Callback callback) {
        if (networkManager == null) {
            return;
        }

        final Dispatcher dispatcher = networkManager.dispatcher();
        if (dispatcher == null) {
            return;
        }
        AsyncHttpUploadCall call = new AsyncHttpUploadCall(callback, true);
        dispatcher.enqueue(call);
        executed = true;
    }

    @Override
    public boolean isExecuted() {
        return executed;
    }

    @Override
    public boolean isCanceled() {
        return canceled;
    }

    @Override
    public Response execute() {
        return getResponseWithInterceptorChain(false);
    }

    @Override
    public Response executeWithNoCustomInterceptor() {
        return getResponseWithInterceptorChain(true);
    }

    @Override
    public Response executeWithAllCustomInterceptor() {
        return null;
    }

    private Response getResponseWithInterceptorChain(boolean withNoCustomInterceptor) {
        Response response = new Response();
        response.setRequest(request);

        if (networkManager == null) {
            return response;
        }

        ArrayList<Interceptor> interceptors;
        if (withNoCustomInterceptor) { // 如果忽略所有自定义的interceptors，则只关心内置的interceptor
            interceptors = networkManager.getBuiltinInterceptors();
        } else {
            interceptors = networkManager.getAllInterceptors();
        }

        if (interceptors == null) {
            return response;
        }

        for (Interceptor itcp : interceptors) {
            // 将旧response传给写一个拦截器，并且拿到新的response
            response = itcp.intercept(request, response);

            if (response != null && response.needBreakOtherInterceptors) {
                // 用完这个标志位，立即将它复位成默认值
                response.resetNeedBreakOtherInterceptors();
                break;
            }
        }

        return response;
    }

    class AsyncHttpUploadCall implements ExecutableCall {

        private final Callback mCallback;

        private final boolean withNoCustomInterceptors;

        AsyncHttpUploadCall(Callback mCallback, boolean withNoCustomInterceptors) {
            this.mCallback = mCallback;
            this.withNoCustomInterceptors = withNoCustomInterceptors;
        }

        @Override
        public void cancel() {
            if (request != null) {
                request.stopRequest();
            }
            canceled = true;
        }

        @Override
        public String getRequestName() {
            String name = "";

            if (request != null) {
                name = request.getClass().getSimpleName();
            }

            return name;
        }

        @Override
        public void run() {
            // 获取线程原来的名字
            final String originName = Thread.currentThread().getName();
            try {
                // 把线程名字设置成当前请求的名字 + 线程id名字
                Thread.currentThread().setName(
                        getRequestName() + "-" + Thread.currentThread().getId());

                if (networkManager != null) {
                    networkManager.registerAsyncHttpCall(AsyncHttpUploadCall.this);
                }

                // 链式处理本请求
                // 不忽略自定义interceptors
                final Response resp = getResponseWithInterceptorChain(withNoCustomInterceptors);

                if (request != null) {
                    // 设置stop的标志位
                    if (request.isStopped()) {
                        resp.isStopped = true;
                    }

                    // 每个request可以自定义结果分发器，如果没自定义，就会用默认的分发器
                    if (request.delivery != null) {
                        request.delivery.deliverResult(resp, mCallback);
                    } else if (networkManager != null && networkManager.delivery() != null) {
                        networkManager.delivery().deliverResult(resp, mCallback);
                    }
                }
            } finally {
                // 把线程设置回原来的名字
                Thread.currentThread().setName(originName);

                if (networkManager != null) {
                    networkManager.unregisterAsyncHttpCall(AsyncHttpUploadCall.this);
                }
            }
        }
    }
}
