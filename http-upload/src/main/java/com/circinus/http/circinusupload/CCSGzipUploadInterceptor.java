package com.circinus.http.circinusupload;

/**
 * Created by ch<PERSON><PERSON> on 2017/11/3.
 */

import com.circinus.http.core.HttpLog;
import com.circinus.http.core.Interceptor;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;

/**
 * 在这里进行gzip压缩操作，存入文件
 */
public class CTIGzipUploadInterceptor implements Interceptor {
    @Override
    public Response intercept(Request request, Response previousResp) {
        if (request instanceof CTIUploadRequest) {
            long timeStart = System.currentTimeMillis();

            File originFile = ((CTIUploadRequest) request).getFile();
            String path = originFile.getParent() + File.separator + originFile.getName() + ".gzip";

            File gzipFile = new File(path);
            if (gzipFile.exists()) {
                gzipFile.delete();
            }

            byte[] buffer = new byte[4096];
            FileInputStream in = null;
            GZIPOutputStream gzip = null;
            FileOutputStream fout = null;
            int len;
            try {
                in = new FileInputStream(originFile);
                fout = new FileOutputStream(path);
                gzip = new GZIPOutputStream(fout);
                while ((len = in.read(buffer)) != -1) {
                    gzip.write(buffer, 0, len);
                }
                gzip.flush();
                ((CTIUploadRequest) request).setFile(new File(path));
                HttpLog.d("HTTP-UPLOAD-GZIP", "GZIP completed: " + path);
            } catch (Exception e) {
                e.printStackTrace();
                HttpLog.e("HTTP-UPLOAD-GZIP", "GZIP failed: " + e.getMessage());
                //压缩失败就要停止后续的流程
                previousResp.needBreakOtherInterceptors = true;
            } finally {
                if (in != null) {
                    try {
                        in.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (gzip != null) {
                    try {
                        gzip.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fout != null) {
                    try {
                        fout.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            HttpLog.d("HTTP-UPLOAD-TIME", "gzip time: " + (System.currentTimeMillis() - timeStart));
        }
        return previousResp;
    }
}
