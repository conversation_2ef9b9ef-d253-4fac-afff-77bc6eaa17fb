package com.circinus.http.circinusupload;

import android.text.TextUtils;

import com.circinus.http.core.HttpHandler;
import com.circinus.http.core.HttpLog;
import com.circinus.http.core.Interceptor;
import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

import org.apache.http.conn.ConnectTimeoutException;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.ListIterator;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSocketFactory;

/**
 * Created by cheneyang on 2017/11/1.
 */

public class CTIHttpUploadInterceptor implements Interceptor {

    private static final String TAG = "HTTP-UPLOAD";

    // 外部可以配置的最大重试次数
    // 外部配置的重试次数不能比这个还打
    private static final int MAX_VALID_RETRY_TIME = 5;

    // 默认的连接超时时间：15s
    private static final int DEFAULT_CONNECT_TIMEOUT = 15000;
    // 默认的socket读超时时间：15s
    private static final int DEFAULT_READ_TIMEOUT = 15000;

    // 默认的最大socket读超时时间：20s
    private static final int DEFAULT_MAX_READ_TIMEOUT = 20000;
    // 默认的最大连接超时时间：20s
    private static final int DEFAULT_MAX_CONNECT_TIMEOUT = 20000;

    private final ArrayList<HttpHandler> httpHandlers = new ArrayList<>();

    private final CTIUploadNetworkManager networkManager;

    CTIHttpUploadInterceptor(CTIUploadNetworkManager networkManager) {
        this.networkManager = networkManager;
    }

    @Override
    public Response intercept(Request request, Response previousResp) {
        // 如果request为空，没法处理，所以直接返回之前的结果
        if (request == null) {
            return previousResp;
        }
        return getResponseFromHttpWithNoretry(request);
    }

    /**
     * 上传不做重试功能
     *
     * @param request 需要发送给服务器的Request
     * @return 从服务器收到的响应
     */
    private Response getResponseFromHttpWithNoretry(Request request) {
        long timeStart = System.currentTimeMillis();
        // 生成一个默认的结果
        Response resp = new Response();

        // 如果request为空，返回一个默认的Response
        if (request == null) {
            return resp;
        }

        // Response里保存了对应的Request
        resp.setRequest(request);

        request.startTime = System.currentTimeMillis();

        // 回调所有HttpHandler的onStart
        callAllHandlerOnStart(request);

        // 方便出现异常时，统一关闭，将这两个io流的声明提前
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        HttpURLConnection httpURLConnection = null;

        try {
            // 重置两个时耗的统计成员，防止发生异常时，上报了上一次的时间统计
            // 比如第一次失败了，getOutputStreamTime 是 10000，然后这次走到某个地方异常了，
            // 没机会更新getOutputStreamTime，此时上报会继续上报上一次的10000这个数
            // 这样是不对的
            request.resetGetOutputStreamTimeAndGetInputStreamTime();

            final String fullURLString = request.getFullURLString();
            HttpLog.d(TAG, "Current http request full url = " + fullURLString);

            // 如果获取到空的URL，直接返回，这里return的话finally语句还是会执行的
            if (TextUtils.isEmpty(fullURLString)) {
                return resp;
            }

            final URL currentURL = new URL(fullURLString);

            // 开始请求，openConnection并不会触发任何TCP包发送
            final URLConnection urlConnection = currentURL.openConnection();
            if (urlConnection == null) {
                return resp;
            }

            if (!(urlConnection instanceof HttpURLConnection)) {
                return resp;
            }

            httpURLConnection = (HttpURLConnection) urlConnection;

            HttpLog.d(TAG, "Create a HttpURLConnection = " + httpURLConnection);

            // 禁用cache
            httpURLConnection.setUseCaches(false);
            // 禁止底层网络库自动重定向
            // 现网用户有可能被重定向至路由器登录界面或者运营商续费界面
            httpURLConnection.setInstanceFollowRedirects(false);

            // 设置Http的超时时间
            setHttpTimeout(httpURLConnection, request);

            // 尝试设置自定义的Https校验流程，如果是Https连接的话
            trySetCustomHttpsVerify(httpURLConnection, request);

            // 设置请求头
            setHeaders(httpURLConnection, request);

            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestMethod("POST");
            httpURLConnection.setDoOutput(true);

            if (request.hasParameters()) {

                // 构建Post的数据
                final String allParams = request.constructAllParams();
                HttpLog.d(TAG, "All param = " + allParams);
                //先计算总共的长度
                final byte[] paramsBytes = allParams.getBytes("UTF-8");

                int contentLength = paramsBytes.length;
                if (request instanceof CTIUploadRequest) {
                    contentLength += ((CTIUploadRequest) request).getFile().length();
                }

                httpURLConnection.setRequestProperty("Content-Length", String.valueOf(contentLength));

                // 加上这个，才会以流的方式发送request body，不然底层会帮你一直缓存着你要发送的东西
                // 等你写完了才会帮你发出去
                httpURLConnection.setFixedLengthStreamingMode(contentLength);

                final long temp = System.currentTimeMillis();
                // 这里是主要的一个时耗点，发起连接，并获取OutputStream以便给服务器发送数据
                final DataOutputStream outStream = new DataOutputStream(
                        httpURLConnection.getOutputStream());

                outStream.write(paramsBytes);
                outStream.flush();
                //writeFile
                HttpLog.d(TAG, "write out file start");
                if (request instanceof CTIUploadRequest) {
                    HttpLog.d(TAG, "write out file");
                    writeFileOut(outStream, (CTIUploadRequest) request);
                }

                request.currentGetOutputStreamTime = System.currentTimeMillis() - temp;

                try {
                    outStream.close();
                } catch (IOException e) {
                    HttpLog.e(TAG, "write out close error" + e.getMessage());
                }
            }

            final int respCode = httpURLConnection.getResponseCode();
            HttpLog.d(TAG, "Network response code = " + respCode);

            resp.resultCode = respCode;

            // 如果请求成功
            if (respCode == HttpURLConnection.HTTP_OK) {
                final long temp = System.currentTimeMillis();
                // 获取Http输入流
                // 这里也是一个主要时耗点，获取服务器返回的数据
                inputStream = httpURLConnection.getInputStream();
                request.currentGetInputStreamTime = System.currentTimeMillis() - temp;

                outputStream = new ByteArrayOutputStream();

                int len; // 每次读的byte数组长度
                byte buf[] = new byte[1024];

                while ((len = inputStream.read(buf)) > 0) {
                    outputStream.write(buf, 0, len);
                }

                final byte[] bytes = outputStream.toByteArray();

                // 使用utf-8编码解析字节流
                resp.responseBody = new String(bytes, "UTF-8");

                HttpLog.d(TAG, "Network response message = " + resp.responseBody);
            }
        } catch (ConnectTimeoutException e) {
            // 服务器建立连接超时
            e.printStackTrace();
            resp.exception = e;
        } catch (SocketTimeoutException e) {
            // 服务器响应连接超时
            e.printStackTrace();
            resp.exception = e;
        } catch (IOException e) {
            e.printStackTrace();
            resp.exception = e;
        } catch (Exception e) {
            // 其他失败后是否尝试
            e.printStackTrace();
            resp.exception = e;
        } finally {
            closeStream(inputStream, outputStream);

            // 防止还没开始连接，就出错了，所以这里要判断httpURLConnection非空
            if (httpURLConnection != null) {
                httpURLConnection.disconnect();
            }

            // 这里某些Handler的回调非常耗时，比如：CentauriIPMeasureHandler(100+ms)
            // 所以计算网络时耗一定要在callAllHandlerOnStop之前
            request.currentTryTimeConsume = System.currentTimeMillis() - request.currentTryTime;

            // 判断是否所有重试都失败了
            if (resp.resultCode != HttpURLConnection.HTTP_OK) {
                request.isAllRetriesFailed = true;
            }

            // 回调所有HttpHandler的onStop
            // 这里某些Handler的回调非常耗时，比如：CentauriIPMeasureHandler(100+ms)
            // 所以计算网络时耗一定要在callAllHandlerOnStop之前
            callAllHandlerOnStop(request, resp);
            HttpLog.d("HTTP-UPLOAD-TIME", "upload time: " + (System.currentTimeMillis() - timeStart));
        }

        return resp;
    }

    private void writeFileOut(DataOutputStream out, CTIUploadRequest request) {
        try {
            HttpLog.d(TAG, "encode file length: " + request.getFile().length());
            FileInputStream fin = new FileInputStream(request.getFile());
            byte[] buffer = new byte[4096];
            int len;
            while ((len = fin.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
            HttpLog.e("HTTP-UPLOAD", "write http out failed: " + Arrays.toString(e.getStackTrace()));
        }
    }

    void addHttpHandler(final HttpHandler httpHandler) {
        if (httpHandler != null) {
            httpHandlers.add(httpHandler);
        }
    }

    /**
     * 通知所有已注册的HttpHandler onStart事件
     */
    private void callAllHandlerOnStart(final Request request) {
        if (httpHandlers.size() == 0) {
            return;
        }

        // 正序遍历
        for (HttpHandler handler : httpHandlers) {
            handler.onHttpStart(request);
        }
    }

    /**
     * 通知所有已注册的HttpHandler onStop事件
     */
    private void callAllHandlerOnStop(final Request request, final Response response) {
        if (httpHandlers.size() == 0) {
            return;
        }

        final int size = httpHandlers.size();

        ListIterator iterator = httpHandlers.listIterator(size);
        // 逆序遍历 onStart用正序，onEnd用逆序，这样不同Handler之间的onStart onEnd不会交叉
        while (iterator.hasPrevious()) {
            final HttpHandler handler = (HttpHandler) iterator.previous();
            handler.onHttpEnd(request, response);
        }
    }

    /**
     * 设置httpURLConnection的超时时间，有默认的就用默认的，如果request单独设置了超时时间，则优先用
     * request单独设置的
     */
    private void setHttpTimeout(final HttpURLConnection httpURLConnection, final Request request) {
        if (httpURLConnection == null) {
            return;
        }

        int connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        int readTimeout = DEFAULT_READ_TIMEOUT;

        if (networkManager != null) {
            connectTimeout = networkManager.defaultConnectTimeout;
            readTimeout = networkManager.defaultReadTimeout;
        }

        if (request != null && request.connectTimeout > 0) {
            connectTimeout = request.connectTimeout;
        }

        if (request != null && request.readTimeout > 0) {
            readTimeout = request.readTimeout;
        }

        // 防止连接超时时间过大
        if (connectTimeout > DEFAULT_MAX_CONNECT_TIMEOUT) {
            connectTimeout = DEFAULT_MAX_CONNECT_TIMEOUT;
        }

        // 防止连接超时时间过小
        if (connectTimeout <= 0) {
            connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        }

        // 防止socket读取超时时间过大
        if (readTimeout > DEFAULT_MAX_READ_TIMEOUT) {
            readTimeout = DEFAULT_READ_TIMEOUT;
        }

        // 防止socket读取超时时间过小
        if (readTimeout <= 0) {
            readTimeout = DEFAULT_READ_TIMEOUT;
        }

        httpURLConnection.setConnectTimeout(connectTimeout);
        httpURLConnection.setReadTimeout(readTimeout);
    }

    /**
     * 如果Request里有设置了自定义的hostnameVerifier和sslSocketFactory
     * 我们在这里把这两个拿出来设置给HttpsURLConnection
     *
     * @param httpURLConnection 对应的HttpsURLConnection
     * @param request           对应的Request
     */
    private static void trySetCustomHttpsVerify(
            final HttpURLConnection httpURLConnection,
            final Request request) {

        if (httpURLConnection == null) {
            return;
        }

        if (request == null) {
            return;
        }

        // 不是Https请求，则不用设置Https证书校验器
        if (!request.isHttpsRequest()) {
            return;
        }

        if (!(httpURLConnection instanceof HttpsURLConnection)) {
            return;
        }

        final HttpsURLConnection httpsURLConnection = (HttpsURLConnection) httpURLConnection;

        final HostnameVerifier hostnameVerifier = request.getCustomHostnameVerifier();
        if (hostnameVerifier != null) {
            httpsURLConnection.setHostnameVerifier(hostnameVerifier);
        }

        final SSLSocketFactory sslSocketFactory = request.getCustomSSLSocketFactory();
        if (sslSocketFactory != null) {
            httpsURLConnection.setSSLSocketFactory(sslSocketFactory);
        }
    }

    /**
     * 设置本次请求的Http request headers
     *
     * @param request 请求的实例，用于获取对应的Headers
     */
    private void setHeaders(final HttpURLConnection connection, final Request request) {
        // 如果request为空，则什么都做不了，直接返回
        if (request == null) {
            return;
        }

        final HashMap<String, String> headers = request.getHttpHeaders();
        // 如果headers为空，则什么都做不了，直接返回
        if (headers == null) {
            return;
        }

        // 如果headers为空，说明上层没设置任何请求头，可以直接返回
        if (headers.size() <= 0) {
            return;
        }

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            final String key = entry.getKey();
            final String value = entry.getValue();
            if (!TextUtils.isEmpty(key)) {
                connection.setRequestProperty(key, value);
            }
        }
    }

    /**
     * 关掉输入输出流
     */
    private void closeStream(final InputStream inputStream, final OutputStream outputStream) {
        try {
            if (inputStream != null) {
                inputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
