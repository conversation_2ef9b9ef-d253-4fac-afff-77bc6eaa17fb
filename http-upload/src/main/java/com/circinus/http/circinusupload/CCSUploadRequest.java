package com.circinus.http.circinusupload;

import com.circinus.http.core.Request;

import java.io.File;

/**
 * Created by <PERSON><PERSON>yang on 2017/11/3.
 */

/**
 * 由于不是标准的multipart/formdata上传协议，所以写在midasupload包里面
 */
public class CTIUploadRequest extends Request {

    private File file;

    @Override
    public String constructAllParams() {
        return super.constructAllParams() + "&content=";
    }

    public void addFileOriginalLength(String actualLen) {
        addHttpParameters("actual_len", actualLen);
    }

    //这里是原文件，在interceptor里面进行Gzip压缩 + Base64编码
    public void setFile(File file) {
        this.file = file;
    }

    public File getFile() {
        return file;
    }

}
